// Generated Dart model for InviteFriendsByIventIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'invitefriendsbyiventiddto_model.freezed.dart';
part 'invitefriendsbyiventiddto_model.g.dart';

@freezed
class InviteFriendsByIventIdDtoModel with _$InviteFriendsByIventIdDtoModel {
  const factory InviteFriendsByIventIdDtoModel({
    required List<String> groupIds,
    required List<String> userIds,
  }) = _InviteFriendsByIventIdDtoModel;

  factory InviteFriendsByIventIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$InviteFriendsByIventIdDtoModelFromJson(json);
}
