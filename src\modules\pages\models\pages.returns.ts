import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { IventCardItem } from 'src/models/ivent-card-item';
import { UserListItem } from 'src/models/user-list-item';
import { VibeFolderCardItem } from 'src/models/vibe-folder-card-item';

export class CreatePageReturn {
  @ApiProperty({ type: 'string' })
  pageId!: string;
}

export class GetIventsCreatedByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(IventCardItem),
    },
  })
  ivents!: IventCardItem[];

  @ApiProperty({ type: 'number' })
  iventCount!: number;
}

export class GetPageByPageIdReturn {
  @ApiProperty({ type: 'string' })
  pageId!: string;

  @ApiProperty({ type: 'string' })
  pageName!: string;

  @ApiProperty({ type: 'string', nullable: true })
  thumbnailUrl!: string | null;

  @ApiProperty({ type: 'string' })
  createdIventCount!: string;

  @ApiProperty({ type: 'string' })
  followerCount!: string;

  @ApiProperty({ type: 'array', items: { type: 'string' } })
  tags!: string[];

  @ApiProperty({ type: 'boolean' })
  haveMembership!: boolean;

  @ApiProperty({ type: 'boolean' })
  isFirstPerson!: boolean;
}

export class GetPageDetailsByPageIdReturn {
  @ApiProperty({ type: 'string', nullable: true })
  description!: string | null;

  @ApiProperty({ type: 'string', nullable: true })
  websiteUrl!: string | null;

  @ApiProperty({ type: 'string' })
  locationId!: string;

  @ApiProperty({ type: 'string', nullable: true })
  locationAdress!: string | null;
}

export class GetVibeFoldersByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(VibeFolderCardItem),
    },
  })
  vibeFolders!: VibeFolderCardItem[];

  @ApiProperty({ type: 'number' })
  vibeFolderCount!: number;
}

export class SearchFollowersByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
  })
  users!: UserListItem[];

  @ApiProperty({ type: 'number' })
  userCount!: number;
}
