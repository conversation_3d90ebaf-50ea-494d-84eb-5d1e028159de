// Generated Dart model for JoinIventAndCreateSquadByIventIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'joiniventandcreatesquadbyiventiddto_model.freezed.dart';
part 'joiniventandcreatesquadbyiventiddto_model.g.dart';

@freezed
class JoinIventAndCreateSquadByIventIdDtoModel with _$JoinIventAndCreateSquadByIventIdDtoModel {
  const factory JoinIventAndCreateSquadByIventIdDtoModel({
    required List<String> groupIds,
    required List<String> userIds,
  }) = _JoinIventAndCreateSquadByIventIdDtoModel;

  factory JoinIventAndCreateSquadByIventIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$JoinIventAndCreateSquadByIventIdDtoModelFromJson(json);
}
