// Generated Dart model for AddModeratorByGroupIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'addmoderatorbygroupiddto_model.freezed.dart';
part 'addmoderatorbygroupiddto_model.g.dart';

@freezed
class AddModeratorByGroupIdDtoModel with _$AddModeratorByGroupIdDtoModel {
  const factory AddModeratorByGroupIdDtoModel({
    required String userId,
  }) = _AddModeratorByGroupIdDtoModel;

  factory AddModeratorByGroupIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$AddModeratorByGroupIdDtoModelFromJson(json);
}
