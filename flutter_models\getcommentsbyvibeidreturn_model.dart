// Generated Dart model for GetCommentsByVibeIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getcommentsbyvibeidreturn_model.freezed.dart';
part 'getcommentsbyvibeidreturn_model.g.dart';

@freezed
class GetCommentsByVibeIdReturnModel with _$GetCommentsByVibeIdReturnModel {
  const factory GetCommentsByVibeIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int commentCount,
  }) = _GetCommentsByVibeIdReturnModel;

  factory GetCommentsByVibeIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetCommentsByVibeIdReturnModelFromJson(json);
}
