import { ApiProperty } from '@nestjs/swagger';
import { AccountTypeEnum, MediaFormatEnum } from 'src/entities';

export class VibeItem {
  @ApiProperty({ type: 'string' })
  vibeId!: string;

  @ApiProperty({ type: 'string' })
  vibeFolderId!: string;

  @ApiProperty({ type: 'string' })
  mediaUrl!: string;

  @ApiProperty({ type: 'enum', enum: Object.values(MediaFormatEnum) })
  mediaFormat!: MediaFormatEnum;

  @ApiProperty({ type: 'string', nullable: true })
  thumbnailUrl!: string | null;

  @ApiProperty({ type: 'string' })
  caption!: string;

  @ApiProperty({ type: 'string' })
  creatorId!: string;

  @ApiProperty({ type: 'string', enum: Object.values(AccountTypeEnum) })
  creatorType!: AccountTypeEnum;

  @ApiProperty({ type: 'string' })
  creatorUsername!: string;

  @ApiProperty({ type: 'string', nullable: true })
  creatorAvatarUrl!: string | null;

  @ApiProperty({ type: 'string' })
  iventId!: string;

  @ApiProperty({ type: 'string' })
  iventName!: string;

  @ApiProperty({ type: 'string' })
  date!: string;

  @ApiProperty({ type: 'number' })
  memberCount!: number;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
  })
  memberNames!: string[];

  @ApiProperty({ type: 'number' })
  likeCount!: number;

  @ApiProperty({ type: 'number' })
  commentCount!: number;

  @ApiProperty({ type: 'string', nullable: true })
  nextVibeId!: string | null;

  @ApiProperty({ type: 'string', nullable: true })
  previousVibeId!: string | null;

  @ApiProperty({ type: 'number' })
  vibeIndex!: number;

  @ApiProperty({ type: 'number' })
  vibeCount!: number;

  @ApiProperty({ type: 'string', nullable: true })
  createdAt!: string | null;
}
