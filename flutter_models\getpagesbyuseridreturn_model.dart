// Generated Dart model for GetPagesByUserIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getpagesbyuseridreturn_model.freezed.dart';
part 'getpagesbyuseridreturn_model.g.dart';

@freezed
class GetPagesByUserIdReturnModel with _$GetPagesByUserIdReturnModel {
  const factory GetPagesByUserIdReturnModel({
    required List<Map<String, dynamic>> pages,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int pageCount,
  }) = _GetPagesByUserIdReturnModel;

  factory GetPagesByUserIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetPagesByUserIdReturnModelFromJson(json);
}
