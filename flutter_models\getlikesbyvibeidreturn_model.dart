// Generated Dart model for GetLikesByVibeIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'getlikesbyvibeidreturn_model.freezed.dart';
part 'getlikesbyvibeidreturn_model.g.dart';

@freezed
class GetLikesByVibeIdReturnModel with _$GetLikesByVibeIdReturnModel {
  const factory GetLikesByVibeIdReturnModel({
    required List<String> type,
  }) = _GetLikesByVibeIdReturnModel;

  factory GetLikesByVibeIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetLikesByVibeIdReturnModelFromJson(json);
}
