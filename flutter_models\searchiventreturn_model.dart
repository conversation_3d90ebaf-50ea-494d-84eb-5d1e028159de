// Generated Dart model for SearchIventReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchiventreturn_model.freezed.dart';
part 'searchiventreturn_model.g.dart';

@freezed
class SearchIventReturnModel with _$SearchIventReturnModel {
  const factory SearchIventReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,
  }) = _SearchIventReturnModel;

  factory SearchIventReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchIventReturnModelFromJson(json);
}
