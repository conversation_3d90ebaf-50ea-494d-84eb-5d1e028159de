// Generated Dart model for GetLevelByUserIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'getlevelbyuseridreturn_model.freezed.dart';
part 'getlevelbyuseridreturn_model.g.dart';

@freezed
class GetLevelByUserIdReturnModel with _$GetLevelByUserIdReturnModel {
  const factory GetLevelByUserIdReturnModel({
    required String levelInfo,
  }) = _GetLevelByUserIdReturnModel;

  factory GetLevelByUserIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetLevelByUserIdReturnModelFromJson(json);
}
