// Generated Dart model for GetFavoritesByUserIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getfavoritesbyuseridreturn_model.freezed.dart';
part 'getfavoritesbyuseridreturn_model.g.dart';

@freezed
class GetFavoritesByUserIdReturnModel with _$GetFavoritesByUserIdReturnModel {
  const factory GetFavoritesByUserIdReturnModel({
    required List<String> ivents,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,
  }) = _GetFavoritesByUserIdReturnModel;

  factory GetFavoritesByUserIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetFavoritesByUserIdReturnModelFromJson(json);
}
