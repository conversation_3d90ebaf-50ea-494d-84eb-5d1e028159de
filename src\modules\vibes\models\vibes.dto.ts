import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsUUID, IsUrl } from 'class-validator';
import { VibePrivacyEnum } from 'src/entities';
import { MediaFormatEnum } from 'src/entities/enums/shared/media-format-enum';

export class CreateVibeDto {
  @ApiProperty({ type: 'string', nullable: true })
  @IsString()
  @IsOptional()
  fileBuffer!: string | null;

  @ApiProperty({ type: 'string', nullable: true })
  @IsUrl()
  @IsOptional()
  mediaUrl!: string | null;

  @ApiProperty({
    type: 'string',
    enum: Object.values(MediaFormatEnum),
  })
  @IsEnum(MediaFormatEnum)
  mediaFormat!: MediaFormatEnum;

  @ApiProperty({ type: 'string', nullable: true })
  @IsString()
  @IsOptional()
  caption!: string | null;

  @ApiProperty({ type: 'string' })
  @IsUUID()
  squadId!: string;

  @ApiProperty({
    type: 'string',
    enum: Object.values(VibePrivacyEnum),
  })
  @IsEnum(VibePrivacyEnum)
  privacy!: VibePrivacyEnum;
}

export class UpdateByVibeIdDto {
  @ApiProperty({ type: 'string' })
  @IsString()
  newCaption!: string;
}
