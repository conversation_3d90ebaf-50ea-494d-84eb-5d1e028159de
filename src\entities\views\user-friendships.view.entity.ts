import { Join<PERSON><PERSON>um<PERSON>, ManyToOne, ViewColumn, ViewEntity } from 'typeorm';
import { UserRelationshipStatusEnum } from '../enums';
import { User } from '../user.entity';

@ViewEntity({
  name: 'user_friendships',
  expression: `
    SELECT
        CASE
            WHEN (ur.sender_id = u.id) THEN ur.sender_id
            ELSE ur.receiver_id
        END AS user_id,
        CASE
            WHEN (ur.sender_id = u.id) THEN ur.receiver_id
            ELSE ur.sender_id
        END AS friend_id,
        COALESCE(ur.updated_at, ur.created_at) AS added_at,
        ur.status
    FROM
        user_relationships ur
        LEFT JOIN users u ON ur.sender_id = u.id
        OR ur.receiver_id = u.id
  `,
})
export class UserFriendships {
  @ViewColumn()
  user_id!: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @ViewColumn()
  friend_id!: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'friend_id' })
  friend?: User;

  @ViewColumn()
  added_at!: Date;

  @ViewColumn()
  status!: UserRelationshipStatusEnum;
}
