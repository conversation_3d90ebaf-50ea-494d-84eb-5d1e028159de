// Generated Dart model for UpdateLinksByPageIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'updatelinksbypageiddto_model.freezed.dart';
part 'updatelinksbypageiddto_model.g.dart';

@freezed
class UpdateLinksByPageIdDtoModel with _$UpdateLinksByPageIdDtoModel {
  const factory UpdateLinksByPageIdDtoModel({
    required String newLink,
  }) = _UpdateLinksByPageIdDtoModel;

  factory UpdateLinksByPageIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$UpdateLinksByPageIdDtoModelFromJson(json);
}
