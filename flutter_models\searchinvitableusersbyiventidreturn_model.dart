// Generated Dart model for SearchInvitableUsersByIventIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchinvitableusersbyiventidreturn_model.freezed.dart';
part 'searchinvitableusersbyiventidreturn_model.g.dart';

@freezed
class SearchInvitableUsersByIventIdReturnModel with _$SearchInvitableUsersByIventIdReturnModel {
  const factory SearchInvitableUsersByIventIdReturnModel({
    required List<Map<String, dynamic>> groups,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int groupCount,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int friendCount,
  }) = _SearchInvitableUsersByIventIdReturnModel;

  factory SearchInvitableUsersByIventIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchInvitableUsersByIventIdReturnModelFromJson(json);
}
