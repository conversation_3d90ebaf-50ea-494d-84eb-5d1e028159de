import { Injectable } from '@nestjs/common';
import { IventCollab } from 'src/entities';
import { EmptyReturn } from 'src/models/empty-return';
import { DataSource } from 'typeorm';
import {
  LeaveCollabrationByIventIdParams,
  RemoveCollabByIventIdParams,
  SearchCollabsForIventCreationParams,
  SearchCollabsParams,
} from './models/ivent-collabs.params';
import { SearchCollabsForIventCreationReturn, SearchCollabsReturn } from './models/ivent-collabs.returns';

@Injectable()
export class IventCollabsService {
  constructor(private dataSource: DataSource) {}

  async searchCollabsForIventCreation(
    searchCollabsForIventCreationParams: SearchCollabsForIventCreationParams,
  ): Promise<SearchCollabsForIventCreationReturn> {
    const { sessionId, sessionRole, q, limit, page } = searchCollabsForIventCreationParams;

    const result = await this.dataSource.query(`
      SELECT
        u.id AS account_id,
        'user' AS account_type,
        u.username AS account_name,
        u.avatar_url AS account_image_url
      FROM users u
      WHERE u.role = 'creator'
        AND u.id != '${sessionId}'
      UNION
      SELECT
        p.id AS account_id,
        'page' AS account_type,
        p.page_name AS account_name,
        p.thumbnail_url AS account_image_url
      FROM pages p;
    `);

    return {
      accounts: result.map((val) => ({
        accountId: val.account_id,
        accountType: val.account_type,
        accountName: val.account_name,
        accountImageUrl: val.account_image_url,
      })),
      accountCount: result.length,
    };
  }

  async searchCollabs(searchCollabsParams: SearchCollabsParams): Promise<SearchCollabsReturn> {
    const { sessionId, sessionRole, iventId, q, limit, page } = searchCollabsParams;

    const qClause = q ? `u.username ILIKE '${q}%' OR p.page_name ILIKE '${q}%'` : 'TRUE';

    const collabs = await this.dataSource
      .getRepository(IventCollab)
      .createQueryBuilder('ic')
      .leftJoinAndSelect('ic.collab_page', 'p')
      .leftJoinAndSelect('ic.collab_user', 'u')
      .leftJoinAndSelect('p.memberships', 'pm', 'pm.member_id = :sessionId', { sessionId })
      .leftJoinAndSelect('u.user_friendships', 'uf', 'uf.friend_id = u.id AND uf.user_id = :sessionId', { sessionId })
      .where('ic.ivent_id = :iventId', { iventId })
      .andWhere("ic.status IN ('admin', 'accepted')")
      .andWhere(qClause)
      .orderBy('ic.updated_at', 'DESC')
      .skip(limit * (page - 1))
      .take(limit)
      .getMany();

    return {
      collabs: collabs.map((ic) => ({
        accountId: (ic.collab_page_id ?? ic.collab_user_id)!,
        accountType: ic.collab_type,
        accountName: (ic.collab_page?.page_name ?? ic.collab_user?.username)!,
        accountImageUrl: (ic.collab_page?.thumbnail_url ?? ic.collab_user?.avatar_url)!,
        membershipStatus: ic.collab_page?.memberships?.[0]?.status ?? null,
        friendshipStatus: ic.collab_user?.user_friendships?.[0]?.status ?? null,
      })),
      collabCount: collabs.length,
    };
  }

  async leaveCollabrationByIventId(
    leaveCollabrationByIventIdParams: LeaveCollabrationByIventIdParams,
  ): Promise<EmptyReturn> {
    const { ...rest } = leaveCollabrationByIventIdParams;
    return {};
  }

  async removeCollabByIventId(removeCollabByIventIdParams: RemoveCollabByIventIdParams): Promise<EmptyReturn> {
    const { ...rest } = removeCollabByIventIdParams;
    return {};
  }
}
