// Generated Dart model for CreateGroupDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'creategroupdto_model.freezed.dart';
part 'creategroupdto_model.g.dart';

@freezed
class CreateGroupDtoModel with _$CreateGroupDtoModel {
  const factory CreateGroupDtoModel({
    required String groupName,
    String? thumbnailBuffer,
    required List<String> userIds,
  }) = _CreateGroupDtoModel;

  factory CreateGroupDtoModel.fromJson(Map<String, dynamic> json) =>
      _$CreateGroupDtoModelFromJson(json);
}
