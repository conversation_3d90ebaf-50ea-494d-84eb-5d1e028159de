export enum UserRoleEnum {
  LEVEL_0 = 'level_0', // Default user role
  LEVEL_1 = 'level_1',
  LEVEL_2 = 'level_2',
  LEVEL_3 = 'level_3',
  LEVEL_4 = 'level_4',
  LEVEL_5 = 'level_5',
  LEVEL_6 = 'level_6',
  CREATOR = 'creator', // Creator role for users who create content
}

export enum UserEduVerificationEnum {
  UNVERIFIED = 'unverified', // User has not verified their education
  STUDENT = 'student', // User is a student
  GRAD = 'grad', // User is a graduate
}

export enum UserGenderEnum {
  MALE = 'male',
  FEMALE = 'female',
  NON_BINARY = 'non-binary',
  OTHER = 'other',
  PREFER_NOT_TO_SAY = 'prefer_not_to_say',
}

export enum UserIventPrivacyEnum {
  PRIVATE = 'private', // Only the user can see their ivents
  FRIENDS = 'friends', // Only friends can see the user's ivents
  PUBLIC = 'public', // Everyone can see the user's ivents
}

export enum UserFriendsPrivacyEnum {
  FRIENDS = 'friends', // Only friends can see the user's friends list
  PUBLIC = 'public', // Everyone can see the user's friends list
}
