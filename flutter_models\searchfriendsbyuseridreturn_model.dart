// Generated Dart model for SearchFriendsByUserIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchfriendsbyuseridreturn_model.freezed.dart';
part 'searchfriendsbyuseridreturn_model.g.dart';

@freezed
class SearchFriendsByUserIdReturnModel with _$SearchFriendsByUserIdReturnModel {
  const factory SearchFriendsByUserIdReturnModel({
    required List<Map<String, dynamic>> groups,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int groupCount,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int friendCount,
  }) = _SearchFriendsByUserIdReturnModel;

  factory SearchFriendsByUserIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchFriendsByUserIdReturnModelFromJson(json);
}
