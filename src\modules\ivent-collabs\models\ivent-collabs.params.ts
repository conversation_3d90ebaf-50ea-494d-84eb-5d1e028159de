export type LeaveCollabrationByIventIdParams = {
  sessionId: string;
  sessionRole: string;
  iventId: string;
};

export type RemoveCollabByIventIdParams = {
  sessionId: string;
  sessionRole: string;
  iventId: string;
  collabId: string;
  collabType: string;
};

export type SearchCollabsForIventCreationParams = {
  sessionId: string;
  sessionRole: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchCollabsParams = {
  sessionId: string;
  sessionRole: string;
  iventId: string;
  q: string;
  limit: number;
  page: number;
};
