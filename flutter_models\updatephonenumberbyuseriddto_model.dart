// Generated Dart model for UpdatePhoneNumberByUserIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'updatephonenumberbyuseriddto_model.freezed.dart';
part 'updatephonenumberbyuseriddto_model.g.dart';

@freezed
class UpdatePhoneNumberByUserIdDtoModel with _$UpdatePhoneNumberByUserIdDtoModel {
  const factory UpdatePhoneNumberByUserIdDtoModel({
    required String newPhoneNumber,
  }) = _UpdatePhoneNumberByUserIdDtoModel;

  factory UpdatePhoneNumberByUserIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$UpdatePhoneNumberByUserIdDtoModelFromJson(json);
}
