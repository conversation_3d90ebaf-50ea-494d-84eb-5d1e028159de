import { Modu<PERSON> } from '@nestjs/common';
import { NotificationsModule } from '../notifications/notifications.module';
import { SquadsModule } from '../squads/squads.module';
import { SquadMembershipsController } from './squad-memberships.controller';
import { SquadMembershipsService } from './squad-memberships.service';

@Module({
  imports: [NotificationsModule, SquadsModule],
  providers: [SquadMembershipsService],
  controllers: [SquadMembershipsController],
  exports: [],
})
export class SquadMembershipsModule {}
