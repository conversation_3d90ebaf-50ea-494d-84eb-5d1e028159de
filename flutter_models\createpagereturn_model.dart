// Generated Dart model for CreatePageReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'createpagereturn_model.freezed.dart';
part 'createpagereturn_model.g.dart';

@freezed
class CreatePageReturnModel with _$CreatePageReturnModel {
  const factory CreatePageReturnModel({
    required String pageId,
  }) = _CreatePageReturnModel;

  factory CreatePageReturnModel.fromJson(Map<String, dynamic> json) =>
      _$CreatePageReturnModelFromJson(json);
}
