import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { ApiResponseObject } from 'src/models/api-response-object';
import { EmptyReturn } from 'src/models/empty-return';
import { CreateVibeDto, UpdateByVibeIdDto } from './models/vibes.dto';
import {
  CreateVibeReturn,
  GetCommentsByVibeIdReturn,
  GetLikesByVibeIdReturn,
  GetVibeByVibeIdReturn,
  GetVibesReturn,
} from './models/vibes.returns';
import { VibesService } from './vibes.service';

@ApiTags('vibes')
@Controller('vibes')
export class VibesController {
  constructor(private readonly vibesService: VibesService) {}

  @ApiOperation({
    summary: 'Vibe oluşturulur',
  })
  @ApiResponseObject({
    model: CreateVibeReturn,
  })
  @Post('create')
  async createVibe(
    @Body() createVibeDto: CreateVibeDto,
    @Res({ passthrough: true }) res: Response,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.createVibe({
      sessionId,
      sessionRole,
      ...createVibeDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe silinir',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Delete(':id/delete')
  async deleteByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.deleteByVibeId({
      sessionId,
      sessionRole,
      vibeId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe sekmesi listelenir',
  })
  @ApiResponseObject({
    model: GetVibesReturn,
  })
  @Get('')
  async getVibes(
    @Res({ passthrough: true }) res: Response,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.getVibes({
      sessionId,
      sessionRole,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe getirilir',
  })
  @ApiResponseObject({
    model: GetVibeByVibeIdReturn,
  })
  @Get(':id')
  async getByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.getByVibeId({
      sessionId,
      sessionRole,
      vibeId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe yorumları listelenir',
  })
  @ApiResponseObject({
    model: GetCommentsByVibeIdReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'number' })
  @ApiQuery({ name: 'page', required: false, type: 'number' })
  @Get(':id/comments')
  async getCommentsByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.getCommentsByVibeId({
      sessionId,
      sessionRole,
      vibeId,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe bilgileri güncellenir',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Put(':id/update')
  async updateByVibeId(
    @Body() updateByVibeIdDto: UpdateByVibeIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.updateByVibeId({
      sessionId,
      sessionRole,
      vibeId,
      ...updateByVibeIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe beğenileri listelenir',
  })
  @ApiResponseObject({
    model: GetLikesByVibeIdReturn,
  })
  @Get(':id/likes')
  async getLikesByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.getLikesByVibeId({
      sessionId,
      sessionRole,
      vibeId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe beğenilir',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':id/likes/like')
  async likeByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.likeByVibeId({
      sessionId,
      sessionRole,
      vibeId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe beğenisi kaldırılır',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':id/likes/unlike')
  async unlikeByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.unlikeByVibeId({
      sessionId,
      sessionRole,
      vibeId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe gizlenir',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':id/hide')
  async hideByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.hideByVibeId({
      sessionId,
      sessionRole,
      vibeId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Vibe IDsi ile vibe gösterime girer',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':id/show')
  async showByVibeId(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) vibeId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.vibesService.showByVibeId({
      sessionId,
      sessionRole,
      vibeId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
