export type SearchModeratorsForPageCreationParams = {
  sessionId: string;
  sessionRole: string;
  sessionPageId: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchModeratorsToAddByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  sessionPageId: string;
  pageId: string;
  q: string;
  limit: number;
  page: number;
};

export type AddPageMembersByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  sessionPageId: string;
  pageId: string;
  userIds: string[];
};

export type JoinPageMembershipByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
};

export type LeavePageMembershipByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
};

export type LeavePageModerationByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  sessionPageId: string;
  pageId: string;
};

export type RemovePageMemberByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  sessionPageId: string;
  pageId: string;
  userId: string;
};

export type RemovePageModeratorByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  sessionPageId: string;
  pageId: string;
  userId: string;
};

export type SearchPageAdminsByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchPageMembersByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  sessionPageId: string;
  pageId: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchUsersToAddByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  sessionPageId: string;
  pageId: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchAdministrationByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  sessionPageId: string;
  pageId: string;
  q: string;
  limit: number;
  page: number;
};

export type TransferPageAdministrationByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  sessionPageId: string;
  pageId: string;
  userId: string;
};
