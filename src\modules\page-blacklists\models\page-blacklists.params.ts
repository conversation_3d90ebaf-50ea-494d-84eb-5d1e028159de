export type BlockUserByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
  userId: string;
};

export type SearchPageBlocklistByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
  q: string;
  limit: number;
  page: number;
};

export type UnblockUserByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
  userId: string;
};
