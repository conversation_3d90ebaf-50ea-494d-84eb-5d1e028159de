// Generated Dart model for RemoveFollowerByUserIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'removefollowerbyuseriddto_model.freezed.dart';
part 'removefollowerbyuseriddto_model.g.dart';

@freezed
class RemoveFollowerByUserIdDtoModel with _$RemoveFollowerByUserIdDtoModel {
  const factory RemoveFollowerByUserIdDtoModel({
    required String followerId,
  }) = _RemoveFollowerByUserIdDtoModel;

  factory RemoveFollowerByUserIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$RemoveFollowerByUserIdDtoModelFromJson(json);
}
