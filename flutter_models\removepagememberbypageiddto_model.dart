// Generated Dart model for RemovePageMemberByPageIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'removepagememberbypageiddto_model.freezed.dart';
part 'removepagememberbypageiddto_model.g.dart';

@freezed
class RemovePageMemberByPageIdDtoModel with _$RemovePageMemberByPageIdDtoModel {
  const factory RemovePageMemberByPageIdDtoModel({
    required String userId,
  }) = _RemovePageMemberByPageIdDtoModel;

  factory RemovePageMemberByPageIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$RemovePageMemberByPageIdDtoModelFromJson(json);
}
