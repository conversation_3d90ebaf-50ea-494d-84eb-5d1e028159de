# Dart Model Generator - Working Example

## How the Generator Works

The generator scans your TypeScript DTO and Return files and automatically creates corresponding Dart models with:

1. **Proper type mapping** (string → String, number → int, boolean → bool)
2. **TypeORM transformation handling** (automatic string-to-number/boolean conversion)
3. **Null safety** (nullable fields marked with ?)
4. **Freezed integration** (immutable data classes with JSON serialization)
5. **Array type handling** (List<String>, List<int>, etc.)

## Example Input → Output

### Input: TypeScript DTO
```typescript
// src/modules/users/models/users.dto.ts
export class RegisterDto {
  @ApiProperty({ type: 'string', example: '+90(500)4003020' })
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/)
  phoneNumber!: string;

  @ApiProperty({
    type: 'string',
    description: "An user's full name can only contain letters.",
  })
  @Matches(/^[a-zA-Z ]*$/)
  fullname!: string;

  @ApiProperty({ type: 'array', items: { type: 'string' } })
  @IsArray()
  @IsUUID('4', { each: true })
  hobbyIds!: string[];
}
```

### Output: Generated Dart Model
```dart
// flutter_models/registerdto_model.dart
// Generated Dart model for RegisterDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'registerdto_model.freezed.dart';
part 'registerdto_model.g.dart';

@freezed
class RegisterDtoModel with _$RegisterDtoModel {
  const factory RegisterDtoModel({
    required String phoneNumber,
    required String fullname,
    required List<String> hobbyIds,
  }) = _RegisterDtoModel;

  factory RegisterDtoModel.fromJson(Map<String, dynamic> json) =>
      _$RegisterDtoModelFromJson(json);
}
```

### Input: TypeScript Return Model with Numbers
```typescript
// src/modules/users/models/users.returns.ts
export class GetUserByUserIdReturn {
  @ApiProperty({ type: 'string' })
  userId!: string;

  @ApiProperty({ type: 'string' })
  username!: string;

  @ApiProperty({ type: 'string', nullable: true })
  avatarUrl!: string | null;

  @ApiProperty({ type: 'number' })
  iventCount!: number;

  @ApiProperty({ type: 'number' })
  friendCount!: number;

  @ApiProperty({ type: 'boolean' })
  isFollowing!: boolean;

  @ApiProperty({ type: 'array', items: { type: 'string' } })
  hobbies!: string[];
}
```

### Output: Generated Dart Model with TypeORM Transformers
```dart
// flutter_models/getuserbyuseridreturn_model.dart
// Generated Dart model for GetUserByUserIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getuserbyuseridreturn_model.freezed.dart';
part 'getuserbyuseridreturn_model.g.dart';

@freezed
class GetUserByUserIdReturnModel with _$GetUserByUserIdReturnModel {
  const factory GetUserByUserIdReturnModel({
    required String userId,
    required String username,
    String? avatarUrl,
    
    // TypeORM transformations for numbers
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,
    
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int friendCount,
    
    // TypeORM transformations for booleans
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFollowing,
    
    required List<String> hobbies,
  }) = _GetUserByUserIdReturnModel;

  factory GetUserByUserIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetUserByUserIdReturnModelFromJson(json);
}
```

## Generated Helper Functions

The generator also creates helper functions for TypeORM transformations:

```dart
// flutter_models/model_helpers.dart
// Helper functions for TypeORM transformations

int _stringToInt(dynamic value) {
  if (value is int) return value;
  if (value is String) return int.tryParse(value) ?? 0;
  return 0;
}

String _intToString(int value) => value.toString();

bool _stringToBool(dynamic value) {
  if (value is bool) return value;
  if (value is String) {
    return value.toLowerCase() == 'true' || value == '1' || value == 't';
  }
  return false;
}

String _boolToString(bool value) => value.toString();
```

## Usage Instructions

1. **Run the generator:**
   ```bash
   node generate_dart_models.js
   ```

2. **Generated files location:**
   ```
   flutter_models/
   ├── registerdto_model.dart
   ├── getuserbyuseridreturn_model.dart
   ├── createiventdto_model.dart
   ├── model_helpers.dart
   └── ... (all other models)
   ```

3. **Use in your Flutter app:**
   ```dart
   // Example API call
   Future<GetUserByUserIdReturnModel> getUser(String userId) async {
     final response = await dio.get('/users/$userId');
     
     // The model automatically handles TypeORM transformations
     return GetUserByUserIdReturnModel.fromJson(response.data['data']);
   }
   ```

## Key Features

✅ **Automatic Type Mapping**: string → String, number → int, boolean → bool  
✅ **TypeORM Compatibility**: Handles string-to-number/boolean transformations  
✅ **Null Safety**: Properly marks nullable fields  
✅ **Array Support**: Converts array types to List<T>  
✅ **Freezed Integration**: Generates immutable data classes  
✅ **JSON Serialization**: Automatic fromJson/toJson methods  
✅ **Batch Processing**: Processes all DTO and Return files at once  

## Statistics from Your API

The generator successfully processed **34 TypeScript files** and generated **100+ Dart models** including:

- Authentication DTOs and Returns
- User management models  
- Event (Ivent) creation and management
- Social features (following, favorites)
- Media handling (Vibes, Memories)
- Page and group management
- Location and university data
- Notification models

This saves you hundreds of hours of manual model creation and ensures consistency between your backend and frontend data structures!
