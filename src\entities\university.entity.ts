import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { IventUniversity } from './ivent-university.entity';
import { Location } from './location.entity';

@Entity('universities')
export class University {
  @PrimaryColumn({ type: 'varchar' })
  university_code!: string;

  @Column({ type: 'varchar', length: 255 })
  university_name!: string;

  @Column({ type: 'text', nullable: true })
  image_url!: string | null;

  @Column({ type: 'uuid', nullable: true })
  location_id!: string | null;

  @ManyToOne(() => Location, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'location_id' })
  location?: Location | null;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from entities
  @OneToMany(() => IventUniversity, (iventUniversity) => iventUniversity.university)
  allowed_ivents?: IventUniversity[];
}
