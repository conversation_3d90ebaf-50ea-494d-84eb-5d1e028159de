// Generated Dart model for AddPageMembersByPageIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'addpagemembersbypageiddto_model.freezed.dart';
part 'addpagemembersbypageiddto_model.g.dart';

@freezed
class AddPageMembersByPageIdDtoModel with _$AddPageMembersByPageIdDtoModel {
  const factory AddPageMembersByPageIdDtoModel({
    required List<String> userIds,
  }) = _AddPageMembersByPageIdDtoModel;

  factory AddPageMembersByPageIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$AddPageMembersByPageIdDtoModelFromJson(json);
}
