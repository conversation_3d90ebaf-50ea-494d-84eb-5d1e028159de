// Generated Dart model for RemovePageModeratorByPageIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'removepagemoderatorbypageiddto_model.freezed.dart';
part 'removepagemoderatorbypageiddto_model.g.dart';

@freezed
class RemovePageModeratorByPageIdDtoModel with _$RemovePageModeratorByPageIdDtoModel {
  const factory RemovePageModeratorByPageIdDtoModel({
    required String userId,
  }) = _RemovePageModeratorByPageIdDtoModel;

  factory RemovePageModeratorByPageIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$RemovePageModeratorByPageIdDtoModelFromJson(json);
}
