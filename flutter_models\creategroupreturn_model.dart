// Generated Dart model for CreateGroupReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'creategroupreturn_model.freezed.dart';
part 'creategroupreturn_model.g.dart';

@freezed
class CreateGroupReturnModel with _$CreateGroupReturnModel {
  const factory CreateGroupReturnModel({
    required String groupId,
  }) = _CreateGroupReturnModel;

  factory CreateGroupReturnModel.fromJson(Map<String, dynamic> json) =>
      _$CreateGroupReturnModelFromJson(json);
}
