#!/usr/bin/env node

/**
 * TypeScript to Dart Model Generator for Ivent API
 *
 * This script analyzes TypeScript DTO and Return files and generates corresponding
 * Dart models with proper JSON serialization.
 *
 * Usage: node generate_dart_models.js
 */

const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  tsSourceDir: './src/modules',
  dartOutputDir: './flutter_models',
  excludeFiles: ['index.ts', 'views', '.params.ts'],
  includePatterns: ['.dto.ts', '.returns.ts'],
};

// TypeScript to Dart type mapping
const TYPE_MAPPING = {
  string: 'String',
  number: 'int',
  boolean: 'bool',
  Date: 'DateTime',
  'string[]': 'List<String>',
  'number[]': 'List<int>',
  'boolean[]': 'List<bool>',
  any: 'dynamic',
  object: 'Map<String, dynamic>',
  array: 'List<dynamic>',
};

// TypeORM transformers that need special handling
const TYPEORM_TRANSFORMERS = {
  stringToNumberTransformer: {
    fromJson: '_stringToInt',
    toJson: '_intToString',
    dartType: 'int',
  },
  stringToBooleanTransformer: {
    fromJson: '_stringToBool',
    toJson: '_boolToString',
    dartType: 'bool',
  },
  stringToArrayTransformer: {
    fromJson: '_stringToArray',
    toJson: '_arrayToString',
    dartType: 'List<String>',
  },
};

class DartModelGenerator {
  constructor() {
    this.generatedModels = new Set();
    this.imports = new Set();
  }

  /**
   * Main generation function
   */
  async generate() {
    console.log('🚀 Starting Dart model generation...');

    // Create output directory
    if (!fs.existsSync(CONFIG.dartOutputDir)) {
      fs.mkdirSync(CONFIG.dartOutputDir, { recursive: true });
    }

    // Process TypeScript DTOs and return models
    await this.processModels();

    // Generate helper functions file
    this.generateHelperFunctions();

    console.log('✅ Dart model generation completed!');
    console.log(`📁 Generated models in: ${CONFIG.dartOutputDir}`);
  }

  /**
   * Process TypeScript entity files
   */
  async processEntities() {
    const entityFiles = fs
      .readdirSync(CONFIG.tsSourceDir)
      .filter((file) => file.endsWith('.entity.ts'))
      .filter((file) => !CONFIG.excludeFiles.includes(file));

    for (const file of entityFiles) {
      const filePath = path.join(CONFIG.tsSourceDir, file);
      const content = fs.readFileSync(filePath, 'utf8');

      const entityInfo = this.parseTypeScriptEntity(content, file);
      if (entityInfo) {
        const dartModel = this.generateDartModel(entityInfo);
        this.writeDartFile(entityInfo.className, dartModel);
      }
    }
  }

  /**
   * Process TypeScript DTO and return model files
   */
  async processModels() {
    console.log('📂 Scanning for DTO and Return files...');

    const modelFiles = this.findModelFiles(CONFIG.tsSourceDir);
    console.log(`📄 Found ${modelFiles.length} files to process`);

    for (const filePath of modelFiles) {
      console.log(`🔍 Processing: ${filePath}`);
      const content = fs.readFileSync(filePath, 'utf8');

      const models = this.parseTypeScriptModels(content, filePath);
      for (const model of models) {
        const dartModel = this.generateDartModel(model);
        this.writeDartFile(model.className, dartModel);
      }
    }
  }

  /**
   * Recursively find all DTO and return files
   */
  findModelFiles(dir) {
    let files = [];

    const items = fs.readdirSync(dir);
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // Skip excluded directories
        if (!CONFIG.excludeFiles.includes(item)) {
          files = files.concat(this.findModelFiles(fullPath));
        }
      } else if (stat.isFile()) {
        // Check if file matches our patterns
        const shouldInclude = CONFIG.includePatterns.some((pattern) => fullPath.includes(pattern));
        const shouldExclude = CONFIG.excludeFiles.some((pattern) => fullPath.includes(pattern));

        if (shouldInclude && !shouldExclude) {
          files.push(fullPath);
        }
      }
    }

    return files;
  }

  /**
   * Parse TypeScript entity file
   */
  parseTypeScriptEntity(content, filename) {
    const className = filename.replace('.entity.ts', '');
    const capitalizedClassName = this.capitalize(className);

    // Extract class definition
    const classMatch = content.match(/@Entity\(['"`]([^'"`]+)['"`]\)\s*export class (\w+)/);
    if (!classMatch) return null;

    const tableName = classMatch[1];
    const actualClassName = classMatch[2];

    // Extract properties
    const properties = this.extractProperties(content);

    return {
      className: actualClassName,
      tableName,
      properties,
      isEntity: true,
    };
  }

  /**
   * Parse TypeScript model files (DTOs, returns)
   */
  parseTypeScriptModels(content, filePath) {
    const models = [];

    // Find all class/interface definitions
    const classMatches = content.matchAll(/export (?:class|interface) (\w+)/g);

    for (const match of classMatches) {
      const className = match[1];
      const properties = this.extractPropertiesFromApiClass(content, className);

      if (properties.length > 0) {
        models.push({
          className,
          properties,
          isEntity: false,
        });
      }
    }

    return models;
  }

  /**
   * Extract properties from class with @ApiProperty decorators using line-by-line parsing
   */
  extractPropertiesFromApiClass(content, className) {
    const properties = [];
    const lines = content.split('\n');

    let inTargetClass = false;
    let braceCount = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Check for target class start
      if (line.includes(`export class ${className}`)) {
        inTargetClass = true;
        braceCount = 0;
        continue;
      }

      if (!inTargetClass) continue;

      // Count braces to know when class ends
      braceCount += (line.match(/\{/g) || []).length;
      braceCount -= (line.match(/\}/g) || []).length;

      // If we've closed all braces, we're out of the class
      if (braceCount < 0) {
        break;
      }

      // Look for @ApiProperty
      if (line.startsWith('@ApiProperty')) {
        const property = this.parseApiPropertyFromLines(lines, i);
        if (property) {
          properties.push(property);
          // Skip the lines we've already processed
          i = property.endLineIndex;
        }
      }
    }

    return properties;
  }

  /**
   * Parse @ApiProperty decorator and following property from lines
   */
  parseApiPropertyFromLines(lines, startIndex) {
    let configLine = lines[startIndex].trim();
    let i = startIndex;

    // Handle multiline @ApiProperty
    while (!configLine.includes(')') && i < lines.length - 1) {
      i++;
      configLine += ' ' + lines[i].trim();
    }

    // Find the property declaration (next non-decorator line)
    i++;
    while (i < lines.length && (lines[i].trim().startsWith('@') || lines[i].trim() === '')) {
      i++;
    }

    if (i >= lines.length) return null;

    const propLine = lines[i].trim();

    // Extract property info
    const propMatch = propLine.match(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*[!:]?\s*([^;=\n]+)/);
    if (!propMatch) return null;

    const propName = propMatch[1];
    const propType = propMatch[2];

    // Extract type from @ApiProperty
    const typeMatch = configLine.match(/type:\s*['"`]([^'"`]+)['"`]/);
    const nullableMatch = configLine.match(/nullable:\s*(true|false)/);
    const itemsMatch = configLine.match(/items:\s*\{\s*type:\s*['"`]([^'"`]+)['"`]/);

    const apiType = typeMatch ? typeMatch[1] : 'string';
    const isNullable = nullableMatch ? nullableMatch[1] === 'true' : false;
    const isArray = apiType === 'array';

    let dartType = this.mapApiTypeToDart(apiType);

    // Handle array types
    if (isArray && itemsMatch) {
      const itemType = this.mapApiTypeToDart(itemsMatch[1]);
      dartType = `List<${itemType}>`;
    } else if (isArray) {
      dartType = 'List<String>'; // default for arrays
    }

    // Check TypeScript type for additional nullable info
    const tsNullable = propType.includes('null') || propType.includes('undefined');

    return {
      name: propName,
      dartType,
      isNullable: isNullable || tsNullable,
      isRequired: !isNullable && !tsNullable,
      needsTransformation: dartType === 'int' || dartType === 'bool',
      transformer: this.getTransformerForType(dartType),
      endLineIndex: i,
    };
  }

  /**
   * Extract properties from TypeScript class/interface
   */
  extractProperties(content) {
    const properties = [];

    // Match property definitions with decorators
    const propertyRegex =
      /@(?:Column|PrimaryGeneratedColumn|CreateDateColumn|UpdateDateColumn|ManyToOne|OneToMany|OneToOne|JoinColumn)\([^)]*\)\s*(?:@[^(]*\([^)]*\)\s*)*([^!:]+)[!:]?\s*([^;=\n]+)/g;

    let match;
    while ((match = propertyRegex.exec(content)) !== null) {
      const propertyName = match[1].trim();
      const typeInfo = match[2].trim();

      const property = this.parsePropertyType(propertyName, typeInfo, match[0]);
      if (property) {
        properties.push(property);
      }
    }

    return properties;
  }

  /**
   * Parse @ApiProperty decorator and extract type information
   */
  parseApiProperty(propertyName, typeInfo, apiPropertyConfig) {
    // Parse the @ApiProperty configuration
    let dartType = 'String'; // default
    let isNullable = false;
    let isArray = false;
    let isRequired = true;

    // Extract type from @ApiProperty({ type: 'string' })
    const typeMatch = apiPropertyConfig.match(/type:\s*['"`]([^'"`]+)['"`]/);
    if (typeMatch) {
      const apiType = typeMatch[1];
      dartType = this.mapApiTypeToDart(apiType);
      isArray = apiType === 'array';
    }

    // Check for nullable
    const nullableMatch = apiPropertyConfig.match(/nullable:\s*(true|false)/);
    if (nullableMatch) {
      isNullable = nullableMatch[1] === 'true';
    }

    // Check for array items type
    if (isArray) {
      const itemsMatch = apiPropertyConfig.match(/items:\s*{\s*type:\s*['"`]([^'"`]+)['"`]/);
      if (itemsMatch) {
        const itemType = this.mapApiTypeToDart(itemsMatch[1]);
        dartType = `List<${itemType}>`;
      } else {
        dartType = 'List<dynamic>';
      }
    }

    // Check TypeScript type for additional info
    if (typeInfo.includes('null') || typeInfo.includes('undefined')) {
      isNullable = true;
    }

    // Handle special cases for numbers that might need transformation
    const needsTransformation = this.needsTypeTransformation(dartType, apiPropertyConfig);

    return {
      name: propertyName,
      dartType,
      isNullable,
      isRequired: !isNullable,
      isArray,
      needsTransformation,
      transformer: needsTransformation ? this.getTransformerForType(dartType) : null,
    };
  }

  /**
   * Map @ApiProperty type to Dart type
   */
  mapApiTypeToDart(apiType) {
    const mapping = {
      string: 'String',
      number: 'int',
      boolean: 'bool',
      array: 'List<dynamic>',
      object: 'Map<String, dynamic>',
    };

    return mapping[apiType] || 'String';
  }

  /**
   * Check if type needs transformation (for TypeORM compatibility)
   */
  needsTypeTransformation(dartType, apiPropertyConfig) {
    // Numbers from API might come as strings due to TypeORM
    return dartType === 'int' || dartType === 'bool';
  }

  /**
   * Get transformer configuration for type
   */
  getTransformerForType(dartType) {
    if (dartType === 'int') {
      return TYPEORM_TRANSFORMERS.stringToNumberTransformer;
    } else if (dartType === 'bool') {
      return TYPEORM_TRANSFORMERS.stringToBooleanTransformer;
    }
    return null;
  }

  /**
   * Parse property type and decorators
   */
  parsePropertyType(name, typeInfo, fullMatch) {
    // Check for transformers
    let transformer = null;
    for (const [transformerName, config] of Object.entries(TYPEORM_TRANSFORMERS)) {
      if (fullMatch.includes(transformerName)) {
        transformer = config;
        break;
      }
    }

    // Determine if nullable
    const isNullable =
      typeInfo.includes('null') || typeInfo.includes('undefined') || fullMatch.includes('nullable: true');

    // Extract base type
    let dartType = this.mapTypeToDart(typeInfo);

    // Apply transformer type if present
    if (transformer) {
      dartType = transformer.dartType;
    }

    return {
      name,
      dartType,
      isNullable,
      isRequired: !isNullable && !fullMatch.includes('optional'),
      transformer,
      isEnum: this.isEnumType(typeInfo),
    };
  }

  /**
   * Map TypeScript type to Dart type
   */
  mapTypeToDart(tsType) {
    // Clean up the type string
    const cleanType = tsType.replace(/[|\s]/g, '').replace('null', '').replace('undefined', '');

    // Check direct mappings
    if (TYPE_MAPPING[cleanType]) {
      return TYPE_MAPPING[cleanType];
    }

    // Handle arrays
    if (cleanType.endsWith('[]')) {
      const elementType = cleanType.slice(0, -2);
      const dartElementType = TYPE_MAPPING[elementType] || this.capitalize(elementType);
      return `List<${dartElementType}>`;
    }

    // Handle enums and custom types
    if (cleanType.includes('Enum')) {
      return this.capitalize(cleanType);
    }

    // Default to capitalized type name (assuming it's a custom class)
    return this.capitalize(cleanType);
  }

  /**
   * Check if type is an enum
   */
  isEnumType(typeInfo) {
    return typeInfo.includes('Enum') || typeInfo.includes('enum');
  }

  /**
   * Generate Dart model from parsed info
   */
  generateDartModel(modelInfo) {
    const { className, properties, isEntity } = modelInfo;
    const modelName = `${className}Model`;

    let dartCode = `// Generated Dart model for ${className}\n`;
    dartCode += `import 'package:freezed_annotation/freezed_annotation.dart';\n\n`;

    // Add helper imports if needed
    if (properties.some((p) => p.transformer)) {
      dartCode += `import 'model_helpers.dart';\n\n`;
    }

    dartCode += `part '${className.toLowerCase()}_model.freezed.dart';\n`;
    dartCode += `part '${className.toLowerCase()}_model.g.dart';\n\n`;

    dartCode += `@freezed\n`;
    dartCode += `class ${modelName} with _$${modelName} {\n`;
    dartCode += `  const factory ${modelName}({\n`;

    // Generate properties
    for (const prop of properties) {
      dartCode += this.generateProperty(prop);
    }

    dartCode += `  }) = _${modelName};\n\n`;
    dartCode += `  factory ${modelName}.fromJson(Map<String, dynamic> json) =>\n`;
    dartCode += `      _$${modelName}FromJson(json);\n`;
    dartCode += `}\n`;

    return dartCode;
  }

  /**
   * Generate individual property
   */
  generateProperty(property) {
    const { name, dartType, isNullable, isRequired, transformer, needsTransformation } = property;

    let propCode = '';

    // Add transformer annotation if needed
    if (needsTransformation && transformer) {
      propCode += `    @JsonKey(fromJson: ${transformer.fromJson}, toJson: ${transformer.toJson})\n`;
    }

    // Add required keyword
    const requiredKeyword = isRequired ? 'required ' : '';

    // Add nullable marker
    const nullableMarker = isNullable ? '?' : '';

    propCode += `    ${requiredKeyword}${dartType}${nullableMarker} ${name},\n`;

    return propCode;
  }

  /**
   * Generate helper functions file
   */
  generateHelperFunctions() {
    const helperCode = `// Helper functions for TypeORM transformations

int _stringToInt(dynamic value) {
  if (value is int) return value;
  if (value is String) return int.tryParse(value) ?? 0;
  return 0;
}

String _intToString(int value) => value.toString();

bool _stringToBool(dynamic value) {
  if (value is bool) return value;
  if (value is String) {
    return value.toLowerCase() == 'true' || value == '1' || value == 't';
  }
  return false;
}

String _boolToString(bool value) => value.toString();

List<String> _stringToArray(dynamic value) {
  if (value is List) return value.cast<String>();
  if (value is String) return value.split(',');
  return [];
}

String _arrayToString(List<String> value) => value.join(',');

DateTime _stringToDateTime(String value) => DateTime.parse(value);
String _dateTimeToString(DateTime value) => value.toIso8601String();
`;

    this.writeDartFile('model_helpers', helperCode, false);
  }

  /**
   * Write Dart file
   */
  writeDartFile(className, content, addExtension = true) {
    const filename = addExtension ? `${className.toLowerCase()}_model.dart` : `${className.toLowerCase()}.dart`;
    const filePath = path.join(CONFIG.dartOutputDir, filename);

    fs.writeFileSync(filePath, content);
    console.log(`✓ Generated: ${filename}`);
  }

  /**
   * Utility: Capitalize first letter
   */
  capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}

// Run the generator
if (require.main === module) {
  const generator = new DartModelGenerator();
  generator.generate().catch(console.error);
}

module.exports = DartModelGenerator;
