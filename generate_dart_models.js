#!/usr/bin/env node

/**
 * TypeScript to Dart Model Generator for Ivent API
 * 
 * This script analyzes TypeScript entity files and generates corresponding
 * Dart models with proper JSON serialization and TypeORM transformations.
 * 
 * Usage: node generate_dart_models.js
 */

const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  tsSourceDir: './src/entities',
  tsModelsDir: './src/models',
  dartOutputDir: './flutter_models',
  excludeFiles: ['index.ts', 'views'],
};

// TypeScript to Dart type mapping
const TYPE_MAPPING = {
  'string': 'String',
  'number': 'int',
  'boolean': 'bool',
  'Date': 'DateTime',
  'string[]': 'List<String>',
  'number[]': 'List<int>',
  'boolean[]': 'List<bool>',
  'any': 'dynamic',
  'object': 'Map<String, dynamic>',
};

// TypeORM transformers that need special handling
const TYPEORM_TRANSFORMERS = {
  'stringToNumberTransformer': {
    fromJson: '_stringToInt',
    toJson: '_intToString',
    dartType: 'int',
  },
  'stringToBooleanTransformer': {
    fromJson: '_stringToBool',
    toJson: '_boolToString',
    dartType: 'bool',
  },
  'stringToArrayTransformer': {
    fromJson: '_stringToArray',
    toJson: '_arrayToString',
    dartType: 'List<String>',
  },
};

class DartModelGenerator {
  constructor() {
    this.generatedModels = new Set();
    this.imports = new Set();
  }

  /**
   * Main generation function
   */
  async generate() {
    console.log('🚀 Starting Dart model generation...');
    
    // Create output directory
    if (!fs.existsSync(CONFIG.dartOutputDir)) {
      fs.mkdirSync(CONFIG.dartOutputDir, { recursive: true });
    }

    // Process TypeScript entities
    await this.processEntities();
    
    // Process TypeScript DTOs and return models
    await this.processModels();
    
    // Generate helper functions file
    this.generateHelperFunctions();
    
    console.log('✅ Dart model generation completed!');
    console.log(`📁 Generated models in: ${CONFIG.dartOutputDir}`);
  }

  /**
   * Process TypeScript entity files
   */
  async processEntities() {
    const entityFiles = fs.readdirSync(CONFIG.tsSourceDir)
      .filter(file => file.endsWith('.entity.ts'))
      .filter(file => !CONFIG.excludeFiles.includes(file));

    for (const file of entityFiles) {
      const filePath = path.join(CONFIG.tsSourceDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      const entityInfo = this.parseTypeScriptEntity(content, file);
      if (entityInfo) {
        const dartModel = this.generateDartModel(entityInfo);
        this.writeDartFile(entityInfo.className, dartModel);
      }
    }
  }

  /**
   * Process TypeScript DTO and return model files
   */
  async processModels() {
    if (!fs.existsSync(CONFIG.tsModelsDir)) return;

    const modelFiles = fs.readdirSync(CONFIG.tsModelsDir, { recursive: true })
      .filter(file => file.endsWith('.ts'))
      .filter(file => !CONFIG.excludeFiles.includes(file));

    for (const file of modelFiles) {
      const filePath = path.join(CONFIG.tsModelsDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      const models = this.parseTypeScriptModels(content, file);
      for (const model of models) {
        const dartModel = this.generateDartModel(model);
        this.writeDartFile(model.className, dartModel);
      }
    }
  }

  /**
   * Parse TypeScript entity file
   */
  parseTypeScriptEntity(content, filename) {
    const className = filename.replace('.entity.ts', '');
    const capitalizedClassName = this.capitalize(className);
    
    // Extract class definition
    const classMatch = content.match(/@Entity\(['"`]([^'"`]+)['"`]\)\s*export class (\w+)/);
    if (!classMatch) return null;

    const tableName = classMatch[1];
    const actualClassName = classMatch[2];

    // Extract properties
    const properties = this.extractProperties(content);
    
    return {
      className: actualClassName,
      tableName,
      properties,
      isEntity: true,
    };
  }

  /**
   * Parse TypeScript model files (DTOs, returns)
   */
  parseTypeScriptModels(content, filename) {
    const models = [];
    
    // Find all class/interface definitions
    const classMatches = content.matchAll(/export (?:class|interface) (\w+)/g);
    
    for (const match of classMatches) {
      const className = match[1];
      const properties = this.extractPropertiesFromClass(content, className);
      
      models.push({
        className,
        properties,
        isEntity: false,
      });
    }
    
    return models;
  }

  /**
   * Extract properties from TypeScript class/interface
   */
  extractProperties(content) {
    const properties = [];
    
    // Match property definitions with decorators
    const propertyRegex = /@(?:Column|PrimaryGeneratedColumn|CreateDateColumn|UpdateDateColumn|ManyToOne|OneToMany|OneToOne|JoinColumn)\([^)]*\)\s*(?:@[^(]*\([^)]*\)\s*)*([^!:]+)[!:]?\s*([^;=\n]+)/g;
    
    let match;
    while ((match = propertyRegex.exec(content)) !== null) {
      const propertyName = match[1].trim();
      const typeInfo = match[2].trim();
      
      const property = this.parsePropertyType(propertyName, typeInfo, match[0]);
      if (property) {
        properties.push(property);
      }
    }
    
    return properties;
  }

  /**
   * Parse property type and decorators
   */
  parsePropertyType(name, typeInfo, fullMatch) {
    // Check for transformers
    let transformer = null;
    for (const [transformerName, config] of Object.entries(TYPEORM_TRANSFORMERS)) {
      if (fullMatch.includes(transformerName)) {
        transformer = config;
        break;
      }
    }

    // Determine if nullable
    const isNullable = typeInfo.includes('null') || typeInfo.includes('undefined') || fullMatch.includes('nullable: true');
    
    // Extract base type
    let dartType = this.mapTypeToDart(typeInfo);
    
    // Apply transformer type if present
    if (transformer) {
      dartType = transformer.dartType;
    }
    
    return {
      name,
      dartType,
      isNullable,
      isRequired: !isNullable && !fullMatch.includes('optional'),
      transformer,
      isEnum: this.isEnumType(typeInfo),
    };
  }

  /**
   * Map TypeScript type to Dart type
   */
  mapTypeToDart(tsType) {
    // Clean up the type string
    const cleanType = tsType.replace(/[|\s]/g, '').replace('null', '').replace('undefined', '');
    
    // Check direct mappings
    if (TYPE_MAPPING[cleanType]) {
      return TYPE_MAPPING[cleanType];
    }
    
    // Handle arrays
    if (cleanType.endsWith('[]')) {
      const elementType = cleanType.slice(0, -2);
      const dartElementType = TYPE_MAPPING[elementType] || this.capitalize(elementType);
      return `List<${dartElementType}>`;
    }
    
    // Handle enums and custom types
    if (cleanType.includes('Enum')) {
      return this.capitalize(cleanType);
    }
    
    // Default to capitalized type name (assuming it's a custom class)
    return this.capitalize(cleanType);
  }

  /**
   * Check if type is an enum
   */
  isEnumType(typeInfo) {
    return typeInfo.includes('Enum') || typeInfo.includes('enum');
  }

  /**
   * Generate Dart model from parsed info
   */
  generateDartModel(modelInfo) {
    const { className, properties, isEntity } = modelInfo;
    const modelName = `${className}Model`;
    
    let dartCode = `// Generated Dart model for ${className}\n`;
    dartCode += `import 'package:freezed_annotation/freezed_annotation.dart';\n\n`;
    
    // Add helper imports if needed
    if (properties.some(p => p.transformer)) {
      dartCode += `import 'model_helpers.dart';\n\n`;
    }
    
    dartCode += `part '${className.toLowerCase()}_model.freezed.dart';\n`;
    dartCode += `part '${className.toLowerCase()}_model.g.dart';\n\n`;
    
    dartCode += `@freezed\n`;
    dartCode += `class ${modelName} with _$${modelName} {\n`;
    dartCode += `  const factory ${modelName}({\n`;
    
    // Generate properties
    for (const prop of properties) {
      dartCode += this.generateProperty(prop);
    }
    
    dartCode += `  }) = _${modelName};\n\n`;
    dartCode += `  factory ${modelName}.fromJson(Map<String, dynamic> json) =>\n`;
    dartCode += `      _$${modelName}FromJson(json);\n`;
    dartCode += `}\n`;
    
    return dartCode;
  }

  /**
   * Generate individual property
   */
  generateProperty(property) {
    const { name, dartType, isNullable, isRequired, transformer } = property;
    
    let propCode = '';
    
    // Add transformer annotation if needed
    if (transformer) {
      propCode += `    @JsonKey(fromJson: ${transformer.fromJson}, toJson: ${transformer.toJson})\n`;
    }
    
    // Add required keyword
    const requiredKeyword = isRequired ? 'required ' : '';
    
    // Add nullable marker
    const nullableMarker = isNullable ? '?' : '';
    
    propCode += `    ${requiredKeyword}${dartType}${nullableMarker} ${name},\n`;
    
    return propCode;
  }

  /**
   * Generate helper functions file
   */
  generateHelperFunctions() {
    const helperCode = `// Helper functions for TypeORM transformations

int _stringToInt(dynamic value) {
  if (value is int) return value;
  if (value is String) return int.tryParse(value) ?? 0;
  return 0;
}

String _intToString(int value) => value.toString();

bool _stringToBool(dynamic value) {
  if (value is bool) return value;
  if (value is String) {
    return value.toLowerCase() == 'true' || value == '1' || value == 't';
  }
  return false;
}

String _boolToString(bool value) => value.toString();

List<String> _stringToArray(dynamic value) {
  if (value is List) return value.cast<String>();
  if (value is String) return value.split(',');
  return [];
}

String _arrayToString(List<String> value) => value.join(',');

DateTime _stringToDateTime(String value) => DateTime.parse(value);
String _dateTimeToString(DateTime value) => value.toIso8601String();
`;

    this.writeDartFile('model_helpers', helperCode, false);
  }

  /**
   * Write Dart file
   */
  writeDartFile(className, content, addExtension = true) {
    const filename = addExtension 
      ? `${className.toLowerCase()}_model.dart`
      : `${className.toLowerCase()}.dart`;
    const filePath = path.join(CONFIG.dartOutputDir, filename);
    
    fs.writeFileSync(filePath, content);
    console.log(`✓ Generated: ${filename}`);
  }

  /**
   * Utility: Capitalize first letter
   */
  capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}

// Run the generator
if (require.main === module) {
  const generator = new DartModelGenerator();
  generator.generate().catch(console.error);
}

module.exports = DartModelGenerator;
