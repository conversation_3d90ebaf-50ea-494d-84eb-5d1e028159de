{"name": "ivent-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.632.0", "@aws-sdk/client-pinpoint": "^3.632.0", "@aws-sdk/client-s3": "^3.633.0", "@aws-sdk/client-ses": "^3.632.0", "@aws-sdk/client-sns": "^3.632.0", "@aws-sdk/s3-request-presigner": "^3.633.0", "@aws-sdk/types": "^3.609.0", "@google-cloud/storage": "^7.16.0", "@nestjs/axios": "^3.0.3", "@nestjs/common": "^10.4.1", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.4.1", "@nestjs/platform-express": "^10.4.1", "@nestjs/swagger": "^7.4.0", "@nestjs/typeorm": "^10.0.2", "@types/bcrypt": "^5.0.2", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.6", "@types/web-push": "^3.6.3", "aws-sdk": "^2.1678.0", "axios": "^1.7.7", "bcrypt": "^5.1.1", "body-parser": "^2.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto-js": "^4.2.0", "csv-parser": "^3.0.0", "firebase-admin": "^13.3.0", "fs": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "multer-s3": "^3.0.1", "mysql2": "^3.11.0", "nanoid": "^5.0.7", "pg": "^8.12.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "swagger-themes": "^1.4.3", "twilio": "^5.7.0", "typeorm": "^0.3.20", "web-push": "^3.6.7"}, "devDependencies": {"@nestjs/cli": "^10.4.4", "@nestjs/schematics": "^10.1.4", "@nestjs/testing": "^10.4.1", "@types/babel__core": "^7.20.5", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/google-cloud__storage": "^1.7.2", "@types/jest": "29.5.12", "@types/multer": "^1.4.11", "@types/node": "^22.4.1", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.2.0", "@typescript-eslint/parser": "^8.2.0", "eslint": "^9.9.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "29.7.0", "prettier": "^3.3.3", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "29.2.4", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "4.2.0", "typescript": "^5.5.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}