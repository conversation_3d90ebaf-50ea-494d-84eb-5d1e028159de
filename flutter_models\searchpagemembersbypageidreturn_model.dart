// Generated Dart model for SearchPageMembersByPageIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchpagemembersbypageidreturn_model.freezed.dart';
part 'searchpagemembersbypageidreturn_model.g.dart';

@freezed
class SearchPageMembersByPageIdReturnModel with _$SearchPageMembersByPageIdReturnModel {
  const factory SearchPageMembersByPageIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int userCount,
  }) = _SearchPageMembersByPageIdReturnModel;

  factory SearchPageMembersByPageIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchPageMembersByPageIdReturnModelFromJson(json);
}
