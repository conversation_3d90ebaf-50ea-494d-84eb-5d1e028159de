// Generated Dart model for CreateVibeDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'createvibedto_model.freezed.dart';
part 'createvibedto_model.g.dart';

@freezed
class CreateVibeDtoModel with _$CreateVibeDtoModel {
  const factory CreateVibeDtoModel({
    String? fileBuffer,
    String? mediaUrl,
    String? caption,
    required String squadId,
  }) = _CreateVibeDtoModel;

  factory CreateVibeDtoModel.fromJson(Map<String, dynamic> json) =>
      _$CreateVibeDtoModelFromJson(json);
}
