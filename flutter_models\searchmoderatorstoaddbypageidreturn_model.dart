// Generated Dart model for SearchModeratorsToAddByPageIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchmoderatorstoaddbypageidreturn_model.freezed.dart';
part 'searchmoderatorstoaddbypageidreturn_model.g.dart';

@freezed
class SearchModeratorsToAddByPageIdReturnModel with _$SearchModeratorsToAddByPageIdReturnModel {
  const factory SearchModeratorsToAddByPageIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int userCount,
  }) = _SearchModeratorsToAddByPageIdReturnModel;

  factory SearchModeratorsToAddByPageIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchModeratorsToAddByPageIdReturnModelFromJson(json);
}
