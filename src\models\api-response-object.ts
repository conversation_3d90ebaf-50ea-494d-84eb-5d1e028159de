import { Type, applyDecorators } from '@nestjs/common';
import { ApiExtraModels, ApiProperty, ApiResponse, ApiResponseOptions, getSchemaPath } from '@nestjs/swagger';

type ApiResponseObjectOptions<T> = {
  model: T;
} & ApiResponseOptions;

export class ResponseObject<T> {
  @ApiProperty({ type: 'number', example: 200 })
  status!: number;

  @ApiProperty({ type: 'string', example: 'Success' })
  message!: string;

  data!: T;
}

export const ApiResponseObject = <T extends Type<any>>(options: ApiResponseObjectOptions<T>) => {
  const { model, description } = options;
  const apiResponseOptions: ApiResponseOptions = {
    schema: {
      title: `ResponseObjectOf${model.name}`,
      allOf: [
        { $ref: getSchemaPath(ResponseObject) },
        {
          type: 'object',
          properties: {
            data: {
              items: { $ref: getSchemaPath(model) },
            },
          },
        },
      ],
    },
    ...(description !== undefined ? { description } : {}),
  };

  return applyDecorators(ApiExtraModels(model), ApiResponse(apiResponseOptions));
};
