// Generated Dart model for GetUserByUserIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getuserbyuseridreturn_model.freezed.dart';
part 'getuserbyuseridreturn_model.g.dart';

@freezed
class GetUserByUserIdReturnModel with _$GetUserByUserIdReturnModel {
  const factory GetUserByUserIdReturnModel({
    required String userId,
    required String userRole,
    required String username,
    required String fullname,
    String? avatarUrl,
    @Json<PERSON>ey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int friendCount,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int followerCount,
    required List<String> hobbies,
    @<PERSON><PERSON><PERSON><PERSON>(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFollowing,
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFriend,
    @<PERSON><PERSON><PERSON><PERSON>(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFirstPerson,
  }) = _GetUserByUserIdReturnModel;

  factory GetUserByUserIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetUserByUserIdReturnModelFromJson(json);
}
