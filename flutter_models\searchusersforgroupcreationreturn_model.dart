// Generated Dart model for SearchUsersForGroupCreationReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchusersforgroupcreationreturn_model.freezed.dart';
part 'searchusersforgroupcreationreturn_model.g.dart';

@freezed
class SearchUsersForGroupCreationReturnModel with _$SearchUsersForGroupCreationReturnModel {
  const factory SearchUsersForGroupCreationReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int userCount,
  }) = _SearchUsersForGroupCreationReturnModel;

  factory SearchUsersForGroupCreationReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchUsersForGroupCreationReturnModelFromJson(json);
}
