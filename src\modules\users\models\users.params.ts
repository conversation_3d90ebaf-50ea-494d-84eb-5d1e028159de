import { UserEduVerificationEnum } from 'src/entities';

export type DeleteUserByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type FollowByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type GetContactsByUserIdParams = {
  phoneNumbers: string[];
  sessionId: string;
  sessionRole: string;
  userId: string;
  limit: number;
  page: number;
};

export type GetFavoritesByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
  q: string;
  limit: number;
  page: number;
};

export type GetFollowerFriendsByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
  q: string;
  limit: number;
  page: number;
};

export type GetFollowersByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
  q: string;
  limit: number;
  page: number;
};

export type GetFollowingsByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
  q: string;
  limit: number;
  page: number;
};

export type GetIventsByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
  type: string;
  q: string;
  limit: number;
  page: number;
};

export type GetLevelByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type GetPagesByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
  limit: number;
  page: number;
};

export type GetMemoryFoldersByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
  limit: number;
  page: number;
};

export type GetUserByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type GetVibeFoldersByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
  limit: number;
  page: number;
};

export type RegisterParams = {
  phoneNumber: string;
  fullname: string;
  hobbyIds: string[];
};

export type RemoveFollowerByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
  followerId: string;
};

export type SavePhoneContactsParams = {
  sessionId: string;
  sessionRole: string;
  phoneNumbers: string[];
};

export type SendCreatorRequestFormParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type SendVerificationEmailParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type SubscribeByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type UnfollowByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type UnsubscribeByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type UpdateByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
  newUsername: string;
  newBirthday: string;
  newGender: string;
  newAvatarUrl: string;
};

export type UpdateEmailByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
  newEmail: string;
};

export type UpdateGradByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
  newGrad: UserEduVerificationEnum;
};

export type UpdateNotificationsByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type UpdatePhoneNumberByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
  newPhoneNumber: string;
};

export type UpdatePrivacyByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type ValidateEmailParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};

export type GetUserBannerByUserIdParams = {
  sessionId: string;
  sessionRole: string;
  userId: string;
};
