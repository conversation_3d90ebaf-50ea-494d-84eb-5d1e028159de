import { ApiProperty } from '@nestjs/swagger';

export class SearchUniversitiesReturn {
  @ApiProperty({
    type: 'array',
    items: {
      type: 'object',
      properties: {
        universityName: { type: 'string' },
        universityImageUrl: { type: 'string' },
        universityLocationState: { type: 'string' },
      },
    },
  })
  universities!: {
    universityName: string;
    universityImageUrl: string;
    universityLocationState: string;
  }[];

  @ApiProperty({ type: 'number' })
  universityCount!: number;
}
