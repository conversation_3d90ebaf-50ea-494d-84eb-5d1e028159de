import { Column, CreateDateColumn, Entity, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Ivent } from './ivent.entity';
import { Page } from './page.entity';
import { University } from './university.entity';

@Entity('locations')
export class Location {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'varchar', length: 255 })
  location_name!: string;

  @Column({ type: 'text' })
  open_address!: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  district!: string | null;

  @Column({ type: 'varchar', nullable: true })
  city!: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true })
  mapbox_id!: string;

  @Column({
    type: 'geography',
    spatialFeatureType: 'Point',
    srid: 4326,
  })
  geom!: {
    type: 'Point';
    coordinates: [number, number]; // [latitude, longitude]
  };

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from entities
  @OneToMany(() => Ivent, (ivent) => ivent.location)
  ivents?: Ivent[];

  @OneToMany(() => Page, (page) => page.location)
  pages?: Page[];

  @OneToMany(() => University, (university) => university.location)
  universities?: University[];
}
