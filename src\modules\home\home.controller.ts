import { <PERSON>, DefaultV<PERSON>uePipe, Get, ParseIntPipe, Query, Res } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { FeedDateEnum } from 'src/constants/enums/feed-date-enum';
import { ApiResponseObject } from 'src/models/api-response-object';
import { HomeService } from './home.service';
import { FeedReturn, MapReturn, SearchAccountReturn, SearchIventReturn } from './models/home.returns';

@ApiTags('home')
@Controller('')
export class HomeController {
  constructor(private readonly homeService: HomeService) {}

  @ApiOperation({
    summary: 'Feedi listeler',
    description:
      'Şu anda feedi listelerken sadece dummy verileri döndürüyoruz. İleride parametre ile çalışacak.',
  })
  @ApiResponseObject({
    model: FeedReturn,
  })
  @ApiQuery({
    name: 'startDate',
    required: true,
    type: 'string',
    example: '2021-12-31',
  })
  @ApiQuery({
    name: 'endDate',
    required: true,
    type: 'string',
    example: '2021-12-31',
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'number' })
  @ApiQuery({ name: 'page', required: false, type: 'number' })
  @Get('feed')
  async feed(
    @Res({ passthrough: true }) res: Response,
    @Query('dateType') dateType: FeedDateEnum,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('categories') categories: string,
    @Query('locationCoeff') locationCoeff: number,
    @Query('latStart') latStart: number,
    @Query('latEnd') latEnd: number,
    @Query('lngStart') lngStart: number,
    @Query('lngEnd') lngEnd: number,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.homeService.feed({
      sessionId,
      sessionRole,
      dateType,
      startDate,
      endDate,
      categories: categories.split(',').filter((x) => x.trim() !== ''),
      locationCoeff,
      latStart,
      latEnd,
      lngStart,
      lngEnd,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Mapi listeler',
    description:
      'Şu anda mapi listelerken sadece dummy verileri döndürüyoruz. İleride parametre ile çalışacak.',
  })
  @ApiResponseObject({
    model: MapReturn,
  })
  @ApiQuery({
    name: 'startDate',
    required: true,
    type: 'string',
    example: '2021-12-31',
  })
  @ApiQuery({
    name: 'endDate',
    required: true,
    type: 'string',
    example: '2021-12-31',
  })
  @ApiQuery({ name: 'limit', required: false, type: 'number' })
  @Get('map')
  async map(
    @Res({ passthrough: true }) res: Response,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('latStart') latStart: number,
    @Query('latEnd') latEnd: number,
    @Query('lngStart') lngStart: number,
    @Query('lngEnd') lngEnd: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.homeService.map({
      sessionId,
      sessionRole,
      startDate,
      endDate,
      latStart,
      latEnd,
      lngStart,
      lngEnd,
      limit,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Uygulamada arama yapar',
  })
  @ApiResponseObject({
    model: SearchIventReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'number' })
  @ApiQuery({ name: 'page', required: false, type: 'number' })
  @Get('searchIvent')
  async searchIvent(
    @Res({ passthrough: true }) res: Response,
    @Query('q') q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.homeService.searchIvent({
      sessionId,
      sessionRole,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Uygulamada arama yapar',
  })
  @ApiResponseObject({
    model: SearchAccountReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'number' })
  @ApiQuery({ name: 'page', required: false, type: 'number' })
  @Get('searchAccount')
  async searchAccount(
    @Res({ passthrough: true }) res: Response,
    @Query('q') q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.homeService.searchAccount({
      sessionId,
      sessionRole,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
