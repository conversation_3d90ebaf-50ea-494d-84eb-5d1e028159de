import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Ivent } from './ivent.entity';
import { Squad } from './squad.entity';
import { Vibe } from './vibe.entity';
import { VibeRankings } from './views';

@Entity('vibe_folders')
export class VibeFolder {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'text', nullable: true })
  thumbnail_url!: string | null;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from entities
  @OneToOne(() => Ivent, (ivent) => ivent.vibe_folder, { nullable: true, onDelete: 'CASCADE' })
  ivent?: Ivent | null;

  @OneToOne(() => Squad, (squad) => squad.vibe_folder, { nullable: true, onDelete: 'CASCADE' })
  squad?: Squad | null;

  @OneToMany(() => Vibe, (vibe) => vibe.vibe_folder)
  vibes?: Vibe[];

  // Additional relationships from views
  @OneToMany(() => VibeRankings, (vibeRankings) => vibeRankings.vibe_folder)
  vibe_rankings?: VibeRankings[];
}
