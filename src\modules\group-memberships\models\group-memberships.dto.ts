import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID } from 'class-validator';

export class AddModeratorByGroupIdDto {
  @ApiProperty({ type: 'string' })
  @IsUUID('4')
  userId!: string;
}

export class InviteMembersByGroupIdDto {
  @ApiProperty({ type: 'array', items: { type: 'string' } })
  @IsArray()
  @IsUUID('4', { each: true })
  userIds!: string[];
}

export class RemoveMemberByGroupIdDto {
  @ApiProperty({ type: 'string' })
  @IsUUID('4')
  userId!: string;
}

export class RemoveModeratorByGroupIdDto {
  @ApiProperty({ type: 'string' })
  @IsUUID('4')
  userId!: string;
}

export class TransferAdministrationByGroupIdDto {
  @ApiProperty({ type: 'string' })
  @IsUUID('4')
  userId!: string;
}
