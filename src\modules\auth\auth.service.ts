import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { sign } from 'jsonwebtoken';
import { AuthEnum } from 'src/constants/enums/auth-enum';
import { User } from 'src/entities';
import { EmptyReturn } from 'src/models/empty-return';
import * as twilio from 'twilio';
import { DataSource } from 'typeorm';
import { LogoutParams, SendVerificationCodeParams, ValidateParams } from './models/auth.params';
import { ValidateReturn } from './models/auth.returns';

@Injectable()
export class AuthService {
  private twilioClient: any;
  private verifyServiceSid: string;

  constructor(
    private dataSource: DataSource,
    private configService: ConfigService,
  ) {
    this.twilioClient = twilio(
      this.configService.get('TWILIO_ACCOUNT_SID'),
      this.configService.get('TWILIO_AUTH_TOKEN'),
    );
    const verifyServiceSid = this.configService.get<string>('TWILIO_VERIFY_SERVICE_SID');
    if (!verifyServiceSid) {
      throw new Error('TWILIO_VERIFY_SERVICE_SID is not defined in environment variables');
    }
    this.verifyServiceSid = verifyServiceSid;
  }

  async logout(logoutParams: LogoutParams): Promise<EmptyReturn> {
    const { ...rest } = logoutParams;
    return {};
  }

  async validate(validateParams: ValidateParams): Promise<ValidateReturn> {
    const { phoneNumber, validationCode } = validateParams;

    // const verificationCheck = await this.twilioClient.verify.v2
    //   .services(this.verifyServiceSid)
    //   .verificationChecks.create({
    //     to: phoneNumber,
    //     code: validationCode,
    //   });

    // const isCodeValid = verificationCheck.status === 'approved';

    // if (!isCodeValid) {
    //   throw new HttpException('BAD_REQUEST', HttpStatus.BAD_REQUEST);
    // }

    const user = await this.dataSource.getRepository(User).findOne({
      where: { phone_number: phoneNumber },
    });

    if (!user) {
      return {
        token: null,
        userId: null,
        role: null,
        username: null,
        fullname: null,
        avatarUrl: null,
        type: AuthEnum.REGISTER,
      };
    } else {
      const jwtSecret = this.configService.get<string>('JWT_SECRET');
      if (!jwtSecret) {
        throw new Error('JWT_SECRET is not defined in environment variables');
      }
      const token = sign({ _id: user.id, role: user.role }, jwtSecret, { expiresIn: '7d' });

      return {
        token,
        userId: user.id,
        role: user.role,
        username: user.username,
        fullname: `${user.firstname} ${user.lastname}`,
        avatarUrl: user.avatar_url,
        type: AuthEnum.LOGIN,
      };
    }
  }

  async sendVerificationCode(sendVerificationCodeParams: SendVerificationCodeParams): Promise<EmptyReturn> {
    const { phoneNumber } = sendVerificationCodeParams;

    // await this.twilioClient.verify.v2
    //   .services(this.verifyServiceSid)
    //   .verifications.create({
    //     to: phoneNumber,
    //     channel: 'sms',
    //   });

    return {};
  }
}
