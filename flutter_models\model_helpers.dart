// Helper functions for TypeORM transformations

int _stringToInt(dynamic value) {
  if (value is int) return value;
  if (value is String) return int.tryParse(value) ?? 0;
  return 0;
}

String _intToString(int value) => value.toString();

bool _stringToBool(dynamic value) {
  if (value is bool) return value;
  if (value is String) {
    return value.toLowerCase() == 'true' || value == '1' || value == 't';
  }
  return false;
}

String _boolToString(bool value) => value.toString();

List<String> _stringToArray(dynamic value) {
  if (value is List) return value.cast<String>();
  if (value is String) return value.split(',');
  return [];
}

String _arrayToString(List<String> value) => value.join(',');

DateTime _stringToDateTime(String value) => DateTime.parse(value);
String _dateTimeToString(DateTime value) => value.toIso8601String();
