// Generated Dart model for UnblockUserByPageIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'unblockuserbypageiddto_model.freezed.dart';
part 'unblockuserbypageiddto_model.g.dart';

@freezed
class UnblockUserByPageIdDtoModel with _$UnblockUserByPageIdDtoModel {
  const factory UnblockUserByPageIdDtoModel({
    required String userId,
  }) = _UnblockUserByPageIdDtoModel;

  factory UnblockUserByPageIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$UnblockUserByPageIdDtoModelFromJson(json);
}
