// Generated Dart model for SearchHobbiesReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchhobbiesreturn_model.freezed.dart';
part 'searchhobbiesreturn_model.g.dart';

@freezed
class SearchHobbiesReturnModel with _$SearchHobbiesReturnModel {
  const factory SearchHobbiesReturnModel({
    required List<Map<String, dynamic>> hobbies,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int hobbyCount,
  }) = _SearchHobbiesReturnModel;

  factory SearchHobbiesReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchHobbiesReturnModelFromJson(json);
}
