name: Deploy to Staging

on:
  push:
    branches: [main]
  pull_request:
    branches: [develop]

jobs:
  build:
    name: Build Image
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ivent_ecr_repo_staging
          IMAGE_TAG: latest
        run: |
          CURRENT_SHA=${GITHUB_SHA::8}
          docker build --no-cache -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:${CURRENT_SHA}
          docker push --all-tags $ECR_REGISTRY/$ECR_REPOSITORY

      - name: Restart ECS Cluster
        env:
          CLUSTER_NAME: ivent-cluster-staging
          SERVICE_NAME: ivent-api-service-default
        run: |
          aws ecs update-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --force-new-deployment