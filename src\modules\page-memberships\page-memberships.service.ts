import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { EmptyReturn } from 'src/models/empty-return';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { DataSource } from 'typeorm';
import {
  AddPageMembersByPageIdParams,
  JoinPageMembershipByPageIdParams,
  LeavePageMembershipByPageIdParams,
  LeavePageModerationByPageIdParams,
  RemovePageMemberByPageIdParams,
  RemovePageModeratorByPageIdParams,
  SearchAdministrationByPageIdParams,
  SearchModeratorsForPageCreationParams,
  SearchModeratorsToAddByPageIdParams,
  SearchPageMembersByPageIdParams,
  SearchUsersToAddByPageIdParams,
  TransferPageAdministrationByPageIdParams,
} from './models/page-memberships.params';
import {
  SearchAdministrationByPageIdReturn,
  SearchModeratorsForPageCreationReturn,
  SearchModeratorsToAddByPageIdReturn,
  SearchPageMembersByPageIdReturn,
  SearchUsersToAddByPageIdReturn,
} from './models/page-memberships.returns';

@Injectable()
export class PageMembershipsService {
  constructor(private dataSource: DataSource) {}

  async searchModeratorsForPageCreation(
    searchModeratorsForPageCreationParams: SearchModeratorsForPageCreationParams,
  ): Promise<SearchModeratorsForPageCreationReturn> {
    const { sessionId, sessionRole, sessionPageId, q, limit, page } = searchModeratorsForPageCreationParams;

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const usersResult = await this.dataSource.query(`
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM user_friendships uf
      LEFT JOIN users u ON u.id = uf.friend_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      WHERE uf.user_id = '${sessionId}'
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      users: usersResult.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_url,
        university: val.university,
      })),
      userCount: usersResult.length,
    };
  }

  async searchModeratorsToAddByPageId(
    searchModeratorsToAddByPageIdParams: SearchModeratorsToAddByPageIdParams,
  ): Promise<SearchModeratorsToAddByPageIdReturn> {
    const { sessionId, sessionRole, sessionPageId, pageId, q, limit, page } = searchModeratorsToAddByPageIdParams;

    const isFirstPerson = sessionPageId === pageId;
    if (!isFirstPerson) throw new HttpException('You are not authorized to view this page.', HttpStatus.BAD_REQUEST);

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const usersResult = await this.dataSource.query(`
      WITH ActivePageMembers AS (
          -- Users who are admins in the page
          SELECT member_id
          FROM page_memberships
          WHERE page_id = '${pageId}'
          AND status IN ('admin', 'moderator')
      )
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM user_friendships uf
      LEFT JOIN users u ON u.id = uf.friend_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      WHERE uf.user_id = '${sessionId}'
      AND uf.friend_id NOT IN (SELECT member_id FROM ActivePageMembers)
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      users: usersResult.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_url,
        university: val.university,
      })),
      userCount: usersResult.length,
    };
  }

  async leavePageModerationByPageId(
    leavePageModerationByPageIdParams: LeavePageModerationByPageIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, sessionPageId, pageId } = leavePageModerationByPageIdParams;

    const isFirstPerson = sessionPageId === pageId;
    if (!isFirstPerson) throw new HttpException('You are not authorized.', HttpStatus.BAD_REQUEST);

    const moderationResult = await this.dataSource.query(`
      SELECT id, member_id, status
      FROM page_memberships
      WHERE status IN ('admin', 'moderator')
      AND page_id = '${pageId}';
    `);
    if (moderationResult.length) {
      throw new HttpException('No result.', HttpStatus.BAD_REQUEST);
    }

    const sessionItem = moderationResult.find((item) => item.member_id === `${sessionId}`);
    if (sessionItem && sessionItem.status === 'admin') {
      const moderatorItem = moderationResult.find((item) => item.status === 'moderator');
      if (!moderatorItem) {
        throw new HttpException('No result.', HttpStatus.BAD_REQUEST);
      }
      await this.dataSource.query(`
        UPDATE page_memberships
        SET status = 'admin'
        WHERE id = '${moderatorItem.id}';
      `);

      await this.dataSource.query(`
        DELETE FROM page_memberships
        WHERE id = '${sessionItem.id}';
      `);
    } else if (sessionItem && sessionItem.status === 'moderator') {
      await this.dataSource.query(`
        DELETE FROM page_memberships
        WHERE id = '${sessionItem.id}';
      `);
    } else {
      throw new HttpException('You are not authorized.', HttpStatus.BAD_REQUEST);
    }

    return {};
  }

  async removePageModeratorByPageId(
    removePageModeratorByPageIdParams: RemovePageModeratorByPageIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, sessionPageId, pageId, userId } = removePageModeratorByPageIdParams;

    const isFirstPerson = sessionPageId === pageId;
    if (!isFirstPerson) throw new HttpException('You are not authorized.', HttpStatus.BAD_REQUEST);

    const moderationResult = await this.dataSource.query(`
      SELECT id, member_id, status
      FROM page_memberships
      WHERE status IN ('admin', 'moderator')
      AND page_id = '${pageId}';
    `);
    if (moderationResult.length) {
      throw new HttpException('No result.', HttpStatus.BAD_REQUEST);
    }

    const sessionItem = moderationResult.find((item) => item.member_id === `${sessionId}`);
    if (!(sessionItem && sessionItem.status === 'admin')) {
      throw new HttpException('You are not authorized.', HttpStatus.BAD_REQUEST);
    }

    const userItem = moderationResult.find((item) => item.member_id === `${userId}`);
    if (!(userItem && userItem.status === 'moderator')) {
      throw new HttpException('You are not authorized.', HttpStatus.BAD_REQUEST);
    }

    await this.dataSource.query(`
      DELETE FROM page_memberships
      WHERE id = '${userItem.id}';
    `);

    return {};
  }

  async transferPageAdministrationByPageId(
    transferPageAdministrationByPageIdParams: TransferPageAdministrationByPageIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, sessionPageId, pageId, userId } = transferPageAdministrationByPageIdParams;

    const isFirstPerson = sessionPageId === pageId;
    if (!isFirstPerson) throw new HttpException('You are not authorized.', HttpStatus.BAD_REQUEST);

    const moderationResult = await this.dataSource.query(`
      SELECT id, member_id, status
      FROM page_memberships
      WHERE status IN ('admin', 'moderator', 'accepted')
      AND page_id = '${pageId}';
    `);
    if (moderationResult.length) {
      throw new HttpException('No result.', HttpStatus.BAD_REQUEST);
    }

    const sessionItem = moderationResult.find((item) => item.member_id === `${sessionId}`);
    if (!(sessionItem && sessionItem.status === 'admin')) {
      throw new HttpException('You are not authorized.', HttpStatus.BAD_REQUEST);
    }

    const userItem = moderationResult.find((item) => item.member_id === `${userId}`);
    if (userItem && ['moderator', 'accepted'].includes(userItem.status)) {
      await this.dataSource.query(`
        UPDATE page_memberships
        SET status = 'admin'
        WHERE id = '${userItem.id}';
      `);

      await this.dataSource.query(`
        UPDATE page_memberships
        SET status = 'moderator'
        WHERE id = '${sessionItem.id}';
      `);
    } else {
      throw new HttpException('You are not authorized.', HttpStatus.BAD_REQUEST);
    }

    return {};
  }

  async searchPageMembersByPageId(
    searchPageMembersByPageIdParams: SearchPageMembersByPageIdParams,
  ): Promise<SearchPageMembersByPageIdReturn> {
    const { sessionId, sessionRole, sessionPageId, pageId, q, limit, page } = searchPageMembersByPageIdParams;

    const isFirstPerson = sessionPageId === pageId;
    if (!isFirstPerson) throw new HttpException('You are not authorized to view this page.', HttpStatus.BAD_REQUEST);

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const membersResult = await this.dataSource.query(`
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM page_memberships pm
      LEFT JOIN users u ON u.id = pm.member_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      WHERE pm.page_id = '${pageId}'
      AND pm.status = 'accepted'
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      users: membersResult.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_url,
        university: val.university,
      })),
      userCount: membersResult.length,
    };
  }

  async searchUsersToAddByPageId(
    searchUsersToAddByPageIdParams: SearchUsersToAddByPageIdParams,
  ): Promise<SearchUsersToAddByPageIdReturn> {
    const { sessionId, sessionRole, sessionPageId, pageId, q, limit, page } = searchUsersToAddByPageIdParams;

    const isFirstPerson = sessionPageId === pageId;
    if (!isFirstPerson) throw new HttpException('You are not authorized to view this page.', HttpStatus.BAD_REQUEST);

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const usersResult = await this.dataSource.query(`
      WITH ActivePageMembers AS (
          -- Users who are members in the page or invited to the page
          SELECT member_id
          FROM page_memberships
          WHERE page_id = '${pageId}'
          AND status IN ('admin', 'moderator', 'accepted', 'pending')
      )
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM user_friendships uf
      LEFT JOIN users u ON u.id = uf.friend_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      WHERE uf.user_id = '${sessionId}'
      AND uf.friend_id NOT IN (SELECT member_id FROM ActivePageMembers)
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      users: usersResult.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_url,
        university: val.university,
      })),
      userCount: usersResult.length,
    };
  }

  async searchAdministrationByPageId(
    searchAdministrationByPageIdParams: SearchAdministrationByPageIdParams,
  ): Promise<SearchAdministrationByPageIdReturn> {
    const { sessionId, sessionRole, sessionPageId, pageId, q, limit, page } = searchAdministrationByPageIdParams;

    const isFirstPerson = sessionPageId === pageId;
    if (!isFirstPerson) throw new HttpException('You are not authorized to view this page.', HttpStatus.BAD_REQUEST);

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const administrationResult = await this.dataSource.query(`
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university,
          pm.status AS role
      FROM page_memberships pm
      LEFT JOIN users u ON u.id = pm.member_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      WHERE pm.page_id = '${pageId}'
      AND pm.status IN ('admin', 'moderator')
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      users: administrationResult.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_url,
        university: val.university,
        role: val.role,
      })),
      userCount: administrationResult.length,
    };
  }

  async joinPageMembershipByPageId(
    joinPageMembershipByPageIdParams: JoinPageMembershipByPageIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, pageId } = joinPageMembershipByPageIdParams;

    const membershipResult = await this.dataSource.query(`
      SELECT id, status
      FROM page_memberships
      WHERE page_id = '${pageId}'
      AND member_id = '${sessionId}';
    `);
    if (membershipResult.find((val) => ['admin', 'moderator', 'accepted'].includes(val.status))) {
      throw new HttpException('Already a valid member', HttpStatus.BAD_REQUEST);
    }

    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'page_memberships',
        values: {
          page_id: pageId,
          member_id: sessionId,
          status: 'accepted',
        },
      }),
    );

    return {};
  }

  async leavePageMembershipByPageId(
    leavePageMembershipByPageIdParams: LeavePageMembershipByPageIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, pageId } = leavePageMembershipByPageIdParams;

    const membershipResult = await this.dataSource.query(`
      SELECT id, status
      FROM page_memberships
      WHERE page_id = '${pageId}'
      AND member_id = '${sessionId}';
    `);
    const sessionItem = membershipResult.find((item) => ['admin', 'moderator', 'accepted'].includes(item.status));
    if (!sessionItem) {
      throw new HttpException('You are not a valid member', HttpStatus.BAD_REQUEST);
    }
    if (sessionItem.status === 'admin') {
      const moderationResult = await this.dataSource.query(`
        SELECT id, member_id, status
        FROM page_memberships
        WHERE status = 'moderator'
        AND page_id = '${pageId}'
        AND member_id != '${sessionId}';
      `);
      if (!moderationResult.length) {
        throw new HttpException('No result.', HttpStatus.BAD_REQUEST);
      }
      const moderatorItem = moderationResult[0];
      await this.dataSource.query(`
        UPDATE page_memberships
        SET status = 'admin'
        WHERE id = '${moderatorItem.id}';
      `);
    }
    await this.dataSource.query(`
      DELETE FROM page_memberships
      WHERE id = '${sessionItem.id}';
    `);

    return {};
  }

  async addPageMembersByPageId(addPageMembersByPageIdParams: AddPageMembersByPageIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, sessionPageId, pageId, userIds } = addPageMembersByPageIdParams;

    const isFirstPerson = sessionPageId === pageId;
    if (!isFirstPerson) throw new HttpException('You are not authorized.', HttpStatus.BAD_REQUEST);

    const membershipResult = await this.dataSource.query(`
      SELECT id, status
      FROM page_memberships
      WHERE page_id = '${pageId}'
      AND member_id IN (${userIds.map((val) => `'${val}'`).join(',')});
    `);
    if (membershipResult.find((val) => ['admin', 'moderator', 'accepted'].includes(val.status))) {
      throw new HttpException('Already a valid member', HttpStatus.BAD_REQUEST);
    }

    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'page_memberships',
        values: userIds.map((val) => ({
          page_id: pageId,
          member_id: val,
          status: 'pending',
          inviter_id: sessionId,
        })),
      }),
    );

    return {};
  }

  async removePageMemberByPageId(removePageMemberByPageIdParams: RemovePageMemberByPageIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, sessionPageId, pageId, userId } = removePageMemberByPageIdParams;

    const isFirstPerson = sessionPageId === pageId;
    if (!isFirstPerson) throw new HttpException('You are not authorized.', HttpStatus.BAD_REQUEST);

    const membershipResult = await this.dataSource.query(`
      SELECT id, status
      FROM page_memberships
      WHERE page_id = '${pageId}'
      AND member_id IN ('${userId}', '${sessionId}')
      AND status IN ('admin', 'moderator', 'accepted');
    `);

    const sessionItem = membershipResult.find((item) => item.member_id === `${sessionId}`);
    const userItem = membershipResult.find((item) => item.member_id === `${userId}`);

    if (userItem && userItem.status === 'moderator') {
      if (!(sessionItem.status !== 'admin')) {
        throw new HttpException('You are not authorized.', HttpStatus.BAD_REQUEST);
      }
      await this.dataSource.query(`
        DELETE FROM page_memberships
        WHERE id = '${userItem.id}';
      `);
    }

    await this.dataSource.query(`
      DELETE FROM page_memberships
      WHERE id = '${userItem.id}';
    `);

    return {};
  }
}
