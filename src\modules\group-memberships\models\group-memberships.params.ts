export type AddModeratorByGroupIdParams = {
  sessionId: string;
  sessionRole: string;
  groupId: string;
  userId: string;
};

export type InviteMembersByGroupIdParams = {
  sessionId: string;
  sessionRole: string;
  groupId: string;
  userIds: string[];
};

export type LeaveGroupByGroupIdParams = {
  sessionId: string;
  sessionRole: string;
  groupId: string;
};

export type RemoveMemberByGroupIdParams = {
  sessionId: string;
  sessionRole: string;
  groupId: string;
  userId: string;
};

export type RemoveModeratorByGroupIdParams = {
  sessionId: string;
  sessionRole: string;
  groupId: string;
  userId: string;
};

export type SearchGroupMembersByGroupIdParams = {
  sessionId: string;
  sessionRole: string;
  groupId: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchInvitableUsersByGroupIdParams = {
  sessionId: string;
  sessionRole: string;
  groupId: string;
  q: string;
  limit: number;
  page: number;
};

export type SearchUsersForGroupCreationParams = {
  sessionId: string;
  sessionRole: string;
  q: string;
  limit: number;
  page: number;
};

export type TransferAdministrationByGroupIdParams = {
  sessionId: string;
  sessionRole: string;
  groupId: string;
  userId: string;
};
