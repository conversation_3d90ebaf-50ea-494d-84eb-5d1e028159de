// Generated Dart model for CreateIventReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'createiventreturn_model.freezed.dart';
part 'createiventreturn_model.g.dart';

@freezed
class CreateIventReturnModel with _$CreateIventReturnModel {
  const factory CreateIventReturnModel({
    required String iventId,
  }) = _CreateIventReturnModel;

  factory CreateIventReturnModel.fromJson(Map<String, dynamic> json) =>
      _$CreateIventReturnModelFromJson(json);
}
