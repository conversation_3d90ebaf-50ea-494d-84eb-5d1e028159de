// Generated Dart model for SearchUsersToAddByPageIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchuserstoaddbypageidreturn_model.freezed.dart';
part 'searchuserstoaddbypageidreturn_model.g.dart';

@freezed
class SearchUsersToAddByPageIdReturnModel with _$SearchUsersToAddByPageIdReturnModel {
  const factory SearchUsersToAddByPageIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int userCount,
  }) = _SearchUsersToAddByPageIdReturnModel;

  factory SearchUsersToAddByPageIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchUsersToAddByPageIdReturnModelFromJson(json);
}
