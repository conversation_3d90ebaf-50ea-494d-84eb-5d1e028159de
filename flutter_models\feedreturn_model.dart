// Generated Dart model for FeedReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'feedreturn_model.freezed.dart';
part 'feedreturn_model.g.dart';

@freezed
class FeedReturnModel with _$FeedReturnModel {
  const factory FeedReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,
  }) = _FeedReturnModel;

  factory FeedReturnModel.fromJson(Map<String, dynamic> json) =>
      _$FeedReturnModelFromJson(json);
}
