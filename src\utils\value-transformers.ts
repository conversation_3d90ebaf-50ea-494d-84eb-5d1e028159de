export const stringToArrayTransformer = {
  from: (value: string | null) => (value ? value.split(',') : []),
  to: (value: string[]) => value.join(','),
};

export const stringToNumberTransformer = {
  from: (value: string | null) => (value ? parseInt(value, 10) : 0),
  to: (value: number) => value.toString(),
};

export const stringToBooleanTransformer = {
  from: (value: string | boolean | null) => {
    if (value === null || value === undefined) return false;
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true' || value === '1' || value === 't';
    }
    return Boolean(value);
  },
  to: (value: boolean) => value.toString(),
};
