// Generated Dart model for RemoveModeratorByGroupIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'removemoderatorbygroupiddto_model.freezed.dart';
part 'removemoderatorbygroupiddto_model.g.dart';

@freezed
class RemoveModeratorByGroupIdDtoModel with _$RemoveModeratorByGroupIdDtoModel {
  const factory RemoveModeratorByGroupIdDtoModel({
    required String userId,
  }) = _RemoveModeratorByGroupIdDtoModel;

  factory RemoveModeratorByGroupIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$RemoveModeratorByGroupIdDtoModelFromJson(json);
}
