import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { UserListItem } from 'src/models/user-list-item';

export class GetUserBlocklistReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
  })
  users!: UserListItem[];

  @ApiProperty({ type: 'number' })
  userCount!: number;
}

export class SearchFriendsByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      type: 'object',
      properties: {
        groupId: { type: 'string' },
        groupName: { type: 'string' },
        memberNames: {
          type: 'array',
          items: { type: 'boolean' },
        },
        memberCount: { type: 'number' },
      },
    },
  })
  groups!: {
    groupId: string;
    groupName: string;
    memberNames: string[];
    memberCount: number;
  }[];

  @ApiProperty({ type: 'number' })
  groupCount!: number;

  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(UserListItem) },
  })
  friends!: UserListItem[];

  @ApiProperty({ type: 'number' })
  friendCount!: number;
}
