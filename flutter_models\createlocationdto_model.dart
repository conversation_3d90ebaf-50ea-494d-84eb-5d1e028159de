// Generated Dart model for CreateLocationDto
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'createlocationdto_model.freezed.dart';
part 'createlocationdto_model.g.dart';

@freezed
class CreateLocationDtoModel with _$CreateLocationDtoModel {
  const factory CreateLocationDtoModel({
    required String locationName,
    required String openAddress,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int coordinates,
    required String state,
  }) = _CreateLocationDtoModel;

  factory CreateLocationDtoModel.fromJson(Map<String, dynamic> json) =>
      _$CreateLocationDtoModelFromJson(json);
}
