import { MemoryOriginEnum } from 'src/constants/enums/memory-origin-enum';
import { MediaFormatEnum } from 'src/entities/enums/shared/media-format-enum';

export type CreateMemoryParams = {
  sessionId: string;
  sessionRole: string;
  mediaFormat: MediaFormatEnum;
  caption: string | null;
  squadId: string;
};

export type DeleteMemoryByMemoryIdParams = {
  sessionId: string;
  sessionRole: string;
  memoryId: string;
};

export type GetMemoryByMemoryIdParams = {
  sessionId: string;
  sessionRole: string;
  memoryId: string;
  origin: MemoryOriginEnum;
};
