import { stringToArrayTransformer, stringToNumberTransformer } from 'src/utils/value-transformers';
import { JoinColumn, ManyToOne, ViewColumn, ViewEntity } from 'typeorm';
import { Ivent } from '../ivent.entity';
import { User } from '../user.entity';
import { SquadFriendships } from './squad-friendships.view.entity';

@ViewEntity({
  name: 'member_summary_of_session_squad',
  expression: `
    SELECT
        sf.member_id AS user_id,
        s.ivent_id,
        count(u.id) AS member_count,
        array_to_string(
            (array_agg(u.firstname)) [:2],
            ','
        ) AS member_first_names,
        array_to_string(
            (array_agg(u.avatar_url)) [:5],
            ','
        ) AS member_avatar_urls
    FROM
        squad_friendships sf
        LEFT JOIN squads s ON s.id = sf.squad_id
        LEFT JOIN users u ON u.id = sf.other_member_id
    GROUP BY
        s.ivent_id,
        sf.member_id
  `,
  dependsOn: [() => SquadFriendships],
})
export class MemberSummaryOfSessionSquad {
  @ViewColumn()
  user_id!: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @ViewColumn()
  ivent_id!: string;

  @ManyToOne(() => Ivent)
  @JoinColumn({ name: 'ivent_id' })
  ivent?: Ivent;

  @ViewColumn({ transformer: stringToNumberTransformer })
  member_count!: number;

  @ViewColumn({ transformer: stringToArrayTransformer })
  member_first_names!: string[];

  @ViewColumn({ transformer: stringToArrayTransformer })
  member_avatar_urls!: (string | null)[];
}
