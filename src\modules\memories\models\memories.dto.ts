import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsString, IsUUID } from 'class-validator';
import { MediaFormatEnum } from 'src/entities/enums/shared/media-format-enum';

export class CreateMemoryDto {
  @ApiProperty({
    type: 'string',
    enum: Object.values(MediaFormatEnum),
  })
  @IsEnum(MediaFormatEnum)
  mediaFormat!: MediaFormatEnum;

  @ApiProperty({ type: 'string', nullable: true })
  @IsString()
  caption!: string | null;

  @ApiProperty({ type: 'string' })
  @IsUUID()
  squadId!: string;
}
