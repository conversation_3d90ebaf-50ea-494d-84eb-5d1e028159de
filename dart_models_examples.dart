// Complete Dart Models for Ivent API
// These models handle TypeORM string transformations and provide type safety

import 'package:freezed_annotation/freezed_annotation.dart';

part 'dart_models_examples.freezed.dart';
part 'dart_models_examples.g.dart';

// ============================================================================
// ENUMS
// ============================================================================

enum AccountType {
  @JsonValue('USER')
  user,
  @JsonValue('PAGE')
  page,
}

enum MediaFormat {
  @JsonValue('IMAGE')
  image,
  @JsonValue('VIDEO')
  video,
  @JsonValue('AUDIO')
  audio,
}

enum IventPrivacy {
  @JsonValue('PUBLIC')
  public,
  @JsonValue('PRIVATE')
  private,
  @JsonValue('FRIENDS_ONLY')
  friendsOnly,
}

enum VibePrivacy {
  @JsonValue('PUBLIC')
  public,
  @JsonValue('PRIVATE')
  private,
  @JsonValue('FRIENDS_ONLY')
  friendsOnly,
}

enum UserRole {
  @JsonValue('USER')
  user,
  @JsonValue('ADMIN')
  admin,
  @JsonValue('MODERATOR')
  moderator,
}

enum UserGender {
  @JsonValue('MALE')
  male,
  @JsonValue('FEMALE')
  female,
  @JsonValue('OTHER')
  other,
  @JsonValue('PREFER_NOT_TO_SAY')
  preferNotToSay,
}

// ============================================================================
// HELPER FUNCTIONS FOR TYPEORM TRANSFORMATIONS
// ============================================================================

int _stringToInt(dynamic value) {
  if (value is int) return value;
  if (value is String) return int.tryParse(value) ?? 0;
  return 0;
}

String _intToString(int value) => value.toString();

bool _stringToBool(dynamic value) {
  if (value is bool) return value;
  if (value is String) {
    return value.toLowerCase() == 'true' || value == '1' || value == 't';
  }
  return false;
}

String _boolToString(bool value) => value.toString();

DateTime _stringToDateTime(String value) => DateTime.parse(value);
String _dateTimeToString(DateTime value) => value.toIso8601String();

// ============================================================================
// USER MODELS
// ============================================================================

@freezed
class UserModel with _$UserModel {
  const factory UserModel({
    required String userId,
    required UserRole userRole,
    required String username,
    required String fullname,
    String? avatarUrl,
    String? phoneNumber,
    String? email,
    UserGender? gender,
    String? bio,
    String? universityCode,
    
    // TypeORM transformations
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,
    
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int friendCount,
    
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int followerCount,
    
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFollowing,
    
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFriend,
    
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFirstPerson,
    
    required List<String> hobbies,
    
    @JsonKey(fromJson: _stringToDateTime, toJson: _dateTimeToString)
    required DateTime createdAt,
    
    @JsonKey(fromJson: _stringToDateTime, toJson: _dateTimeToString)
    required DateTime updatedAt,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);
}

@freezed
class RegisterDto with _$RegisterDto {
  const factory RegisterDto({
    required String phoneNumber,
    required String fullname,
    required List<String> hobbyIds,
  }) = _RegisterDto;

  factory RegisterDto.fromJson(Map<String, dynamic> json) =>
      _$RegisterDtoFromJson(json);
}

@freezed
class RegisterReturn with _$RegisterReturn {
  const factory RegisterReturn({
    required String userId,
    required String token,
    required String role,
    required String username,
    required String fullname,
    String? avatarUrl,
  }) = _RegisterReturn;

  factory RegisterReturn.fromJson(Map<String, dynamic> json) =>
      _$RegisterReturnFromJson(json);
}

// ============================================================================
// IVENT MODELS
// ============================================================================

@freezed
class IventModel with _$IventModel {
  const factory IventModel({
    required String iventId,
    required String iventName,
    String? thumbnailUrl,
    String? description,
    required String locationName,
    required double latitude,
    required double longitude,
    String? openAddress,
    required String creatorId,
    required AccountType creatorType,
    required String creatorName,
    String? creatorImageUrl,
    required IventPrivacy privacy,
    required String categoryTagId,
    required List<String> tags,
    required List<String> dates,
    
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    bool? isFavorited,
    
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int participantCount,
    
    @JsonKey(fromJson: _stringToDateTime, toJson: _dateTimeToString)
    required DateTime createdAt,
  }) = _IventModel;

  factory IventModel.fromJson(Map<String, dynamic> json) =>
      _$IventModelFromJson(json);
}

@freezed
class CreateIventDto with _$CreateIventDto {
  const factory CreateIventDto({
    required AccountType creatorType,
    required String iventName,
    String? thumbnailUrl,
    String? thumbnailBuffer,
    required List<String> dates,
    String? locationId,
    String? mapboxId,
    required double latitude,
    required double longitude,
    required String locationName,
    String? openAddress,
    String? description,
    required String categoryTagId,
    required List<String> tags,
    required IventPrivacy privacy,
    required List<String> universityIds,
    required List<String> groupIds,
    required List<CollabDto> collabs,
  }) = _CreateIventDto;

  factory CreateIventDto.fromJson(Map<String, dynamic> json) =>
      _$CreateIventDtoFromJson(json);
}

@freezed
class CollabDto with _$CollabDto {
  const factory CollabDto({
    required String id,
    required AccountType type,
  }) = _CollabDto;

  factory CollabDto.fromJson(Map<String, dynamic> json) =>
      _$CollabDtoFromJson(json);
}

// ============================================================================
// VIBE MODELS
// ============================================================================

@freezed
class VibeModel with _$VibeModel {
  const factory VibeModel({
    required String vibeId,
    required String vibeFolderId,
    required MediaFormat mediaFormat,
    String? thumbnailUrl,
    String? mediaUrl,
    required AccountType creatorType,
    String? caption,
    required String creatorId,
    required String creatorUsername,
    String? creatorAvatarUrl,
    required String iventId,
    required String iventName,
    required VibePrivacy privacy,
    
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int likeCount,
    
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int commentCount,
    
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isLiked,
    
    @JsonKey(fromJson: _stringToDateTime, toJson: _dateTimeToString)
    required DateTime createdAt,
  }) = _VibeModel;

  factory VibeModel.fromJson(Map<String, dynamic> json) =>
      _$VibeModelFromJson(json);
}

@freezed
class CreateVibeDto with _$CreateVibeDto {
  const factory CreateVibeDto({
    String? fileBuffer,
    String? mediaUrl,
    required MediaFormat mediaFormat,
    String? caption,
    required String squadId,
    required VibePrivacy privacy,
  }) = _CreateVibeDto;

  factory CreateVibeDto.fromJson(Map<String, dynamic> json) =>
      _$CreateVibeDtoFromJson(json);
}

// ============================================================================
// PAGE MODELS
// ============================================================================

@freezed
class PageModel with _$PageModel {
  const factory PageModel({
    required String pageId,
    required String pageName,
    String? thumbnailUrl,
    String? websiteUrl,
    String? description,
    required String creatorId,
    String? locationId,
    String? locationName,
    
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isEdu,
    
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool haveMembership,
    
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFollowing,
    
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int followerCount,
    
    @JsonKey(fromJson: _stringToDateTime, toJson: _dateTimeToString)
    required DateTime createdAt,
  }) = _PageModel;

  factory PageModel.fromJson(Map<String, dynamic> json) =>
      _$PageModelFromJson(json);
}

// ============================================================================
// MEMORY MODELS
// ============================================================================

@freezed
class MemoryModel with _$MemoryModel {
  const factory MemoryModel({
    required String memoryId,
    required MediaFormat mediaFormat,
    String? thumbnailUrl,
    required AccountType creatorType,
    String? caption,
    required String creatorId,
    required String memoryFolderId,
    
    @JsonKey(fromJson: _stringToDateTime, toJson: _dateTimeToString)
    required DateTime createdAt,
  }) = _MemoryModel;

  factory MemoryModel.fromJson(Map<String, dynamic> json) =>
      _$MemoryModelFromJson(json);
}

// ============================================================================
// COMMENT MODELS
// ============================================================================

@freezed
class CommentModel with _$CommentModel {
  const factory CommentModel({
    required String commentId,
    required String comment,
    required String commenterId,
    required AccountType commenterType,
    required String commenterName,
    String? commenterAvatarUrl,
    
    @JsonKey(fromJson: _stringToDateTime, toJson: _dateTimeToString)
    required DateTime createdAt,
  }) = _CommentModel;

  factory CommentModel.fromJson(Map<String, dynamic> json) =>
      _$CommentModelFromJson(json);
}
