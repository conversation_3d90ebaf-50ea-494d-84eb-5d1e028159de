import { Body, Controller, Param, ParseUUI<PERSON>ipe, Post, Res } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { ApiResponseObject } from 'src/models/api-response-object';
import { EmptyReturn } from 'src/models/empty-return';
import { AuthService } from './auth.service';
import { SendVerificationCodeDto, ValidateDto } from './models/auth.dto';
import { ValidateReturn } from './models/auth.returns';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @ApiOperation({
    summary: 'Hesaptan çıkış yapar',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post('logout')
  async logout(
    @Res({ passthrough: true }) res: Response,
    @Param('id', new ParseUUIDPipe({ version: '4' })) userId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.authService.logout({
      sessionId,
      sessionRole,
      userId,
    });
    res.clearCookie('auth-token');
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary:
      'Telefon numarasına gönderilmiş onay kodunu doğrular ve telefon numarası yeniyse onu kayıt olma sayfasına, değilse giriş sayfasına yönlendirir',
  })
  @ApiResponseObject({
    model: ValidateReturn,
  })
  @Post('validate')
  async validate(@Body() validateDto: ValidateDto) {
    const result = await this.authService.validate({
      ...validateDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Telefon numarasına onay kodu gönderir',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post('send-verification-code')
  async sendVerificationCode(@Body() sendVerificationCodeDto: SendVerificationCodeDto) {
    const result = await this.authService.sendVerificationCode({
      ...sendVerificationCodeDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
