import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Ivent } from './ivent.entity';
import { User } from './user.entity';

@Entity('user_favorites')
export class UserFavorite {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'uuid' })
  user_id!: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @Column({ type: 'uuid' })
  favorited_ivent_id!: string;

  @ManyToOne(() => Ivent, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'favorited_ivent_id' })
  favorited_ivent?: Ivent;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;
}
