// Generated Dart model for GetPageByPageIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getpagebypageidreturn_model.freezed.dart';
part 'getpagebypageidreturn_model.g.dart';

@freezed
class GetPageByPageIdReturnModel with _$GetPageByPageIdReturnModel {
  const factory GetPageByPageIdReturnModel({
    required String pageId,
    required String pageName,
    String? thumbnailUrl,
    required String createdIventCount,
    required String followerCount,
    required List<String> tags,
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool haveMembership,
    @J<PERSON><PERSON><PERSON>(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFirstPerson,
  }) = _GetPageByPageIdReturnModel;

  factory GetPageByPageIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetPageByPageIdReturnModelFromJson(json);
}
