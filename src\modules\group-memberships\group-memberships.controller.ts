import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { ApiResponseObject } from 'src/models/api-response-object';
import { EmptyReturn } from 'src/models/empty-return';
import { GroupMembershipsService } from './group-memberships.service';
import {
  AddModeratorByGroupIdDto,
  InviteMembersByGroupIdDto,
  RemoveMemberByGroupIdDto,
  RemoveModeratorByGroupIdDto,
  TransferAdministrationByGroupIdDto,
} from './models/group-memberships.dto';
import {
  SearchGroupMembersByGroupIdReturn,
  SearchUsersForGroupCreationReturn,
} from './models/group-memberships.returns';

@ApiTags('groupMemberships')
@Controller('groupMemberships')
export class GroupMembershipsController {
  constructor(private readonly groupMembershipsService: GroupMembershipsService) {}

  @ApiOperation({
    summary: 'Grubun IDsi ile arkadaş grubundaki birisinin hesabını yönetici yapar',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':groupId/administration/add')
  async addModeratorByGroupId(
    @Body()
    addModeratorByGroupIdDto: AddModeratorByGroupIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.groupMembershipsService.addModeratorByGroupId({
      sessionId,
      sessionRole,
      groupId,
      ...addModeratorByGroupIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Grubun IDsi ile arkadaş grubundaki birisini yöneticilerden çıkartır',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':groupId/administration/remove')
  async removeModeratorByGroupId(
    @Body()
    removeModeratorByGroupIdDto: RemoveModeratorByGroupIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.groupMembershipsService.removeModeratorByGroupId({
      sessionId,
      sessionRole,
      groupId,
      ...removeModeratorByGroupIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Grubun IDsi ile arkadaş grubundaki birisine yöneticiliği devreder',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':groupId/administration/transfer')
  async transferAdministrationByGroupId(
    @Body()
    transferAdministrationByGroupIdDto: TransferAdministrationByGroupIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.groupMembershipsService.transferAdministrationByGroupId({
      sessionId,
      sessionRole,
      groupId,
      ...transferAdministrationByGroupIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  // Gruba yeni üye aranırken kullanılır
  @ApiOperation({
    summary: 'Arkadaş grubu oluşturulurken eklenebilecek hesapları listeler',
  })
  @ApiResponseObject({
    model: SearchUsersForGroupCreationReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'number' })
  @ApiQuery({ name: 'page', required: false, type: 'number' })
  @Get('search/:groupId')
  async searchInvitableUsersByGroupId(
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.groupMembershipsService.searchInvitableUsersByGroupId({
      sessionId,
      sessionRole,
      groupId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  // Grup oluşturulurken üye aranması için kullanılır
  @ApiOperation({
    summary: 'Arkadaş grubu oluşturulurken eklenebilecek hesapları listeler',
  })
  @ApiResponseObject({
    model: SearchUsersForGroupCreationReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'number' })
  @ApiQuery({ name: 'page', required: false, type: 'number' })
  @Get('search')
  async searchUsersForGroupCreation(
    @Res({ passthrough: true }) res: Response,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.groupMembershipsService.searchUsersForGroupCreation({
      sessionId,
      sessionRole,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Arkadaş grubundaki üyeleri listeler',
  })
  @ApiResponseObject({
    model: SearchGroupMembersByGroupIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'number' })
  @ApiQuery({ name: 'page', required: false, type: 'number' })
  @Get(':groupId/search')
  async searchGroupMembersByGroupId(
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.groupMembershipsService.searchGroupMembersByGroupId({
      sessionId,
      sessionRole,
      groupId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Grubun IDsi ile arkadaş grubundan ayrılınır',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':groupId/leave')
  async leaveGroupByGroupId(
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.groupMembershipsService.leaveGroupByGroupId({
      sessionId,
      sessionRole,
      groupId,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Grubun IDsi ile bir hesaba davet gönderilir',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':groupId/invite')
  async inviteMembersByGroupId(
    @Body()
    inviteMembersByGroupIdDto: InviteMembersByGroupIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.groupMembershipsService.inviteMembersByGroupId({
      sessionId,
      sessionRole,
      groupId,
      ...inviteMembersByGroupIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Grubun IDsi ile bir hesap gruptan çıkartılır',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':groupId/remove')
  async removeMemberByGroupId(
    @Body()
    removeMemberByGroupIdDto: RemoveMemberByGroupIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('groupId', new ParseUUIDPipe({ version: '4' })) groupId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.groupMembershipsService.removeMemberByGroupId({
      sessionId,
      sessionRole,
      groupId,
      ...removeMemberByGroupIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
