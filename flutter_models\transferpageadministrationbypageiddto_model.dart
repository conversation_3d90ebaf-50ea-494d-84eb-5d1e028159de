// Generated Dart model for TransferPageAdministrationByPageIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'transferpageadministrationbypageiddto_model.freezed.dart';
part 'transferpageadministrationbypageiddto_model.g.dart';

@freezed
class TransferPageAdministrationByPageIdDtoModel with _$TransferPageAdministrationByPageIdDtoModel {
  const factory TransferPageAdministrationByPageIdDtoModel({
    required String userId,
  }) = _TransferPageAdministrationByPageIdDtoModel;

  factory TransferPageAdministrationByPageIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$TransferPageAdministrationByPageIdDtoModelFromJson(json);
}
