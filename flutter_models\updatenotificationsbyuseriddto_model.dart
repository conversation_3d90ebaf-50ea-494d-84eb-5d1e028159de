// Generated Dart model for UpdateNotificationsByUserIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'updatenotificationsbyuseriddto_model.freezed.dart';
part 'updatenotificationsbyuseriddto_model.g.dart';

@freezed
class UpdateNotificationsByUserIdDtoModel with _$UpdateNotificationsByUserIdDtoModel {
  const factory UpdateNotificationsByUserIdDtoModel({
    required String newPhoneNumber,
  }) = _UpdateNotificationsByUserIdDtoModel;

  factory UpdateNotificationsByUserIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$UpdateNotificationsByUserIdDtoModelFromJson(json);
}
