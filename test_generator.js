#!/usr/bin/env node

/**
 * Simple test to debug the generator
 */

const fs = require('fs');

// Test with RegisterDto
const content = fs.readFileSync('./src/modules/users/models/users.dto.ts', 'utf8');

console.log('=== TESTING REGEX PATTERNS ===');

// Test 1: Find all export class statements
console.log('\n1. Finding export class statements:');
const classMatches = content.matchAll(/export (?:class|interface) (\w+)/g);
for (const match of classMatches) {
  console.log(`Found class: ${match[1]}`);
}

// Test 2: Extract RegisterDto class body
console.log('\n2. Extracting RegisterDto class body:');
const registerDtoRegex = /export class RegisterDto\s*\{([\s\S]*?)\}/;
const registerDtoMatch = content.match(registerDtoRegex);

if (registerDtoMatch) {
  console.log('RegisterDto class body found:');
  console.log('---START---');
  console.log(registerDtoMatch[1]);
  console.log('---END---');
  
  // Test 3: Find @ApiProperty decorators in the class body
  console.log('\n3. Finding @ApiProperty decorators:');
  const classBody = registerDtoMatch[1];
  
  // Simple approach: find each @ApiProperty block
  const apiPropertyRegex = /@ApiProperty\s*\(\s*\{([^}]*)\}\s*\)[\s\S]*?([a-zA-Z_$][a-zA-Z0-9_$]*)\s*[!:]?\s*([^;=\n]+)/g;
  
  let match;
  while ((match = apiPropertyRegex.exec(classBody)) !== null) {
    console.log(`Property found:`);
    console.log(`  Config: ${match[1]}`);
    console.log(`  Name: ${match[2]}`);
    console.log(`  Type: ${match[3]}`);
    console.log('---');
  }
} else {
  console.log('RegisterDto class not found');
}

// Test 4: Alternative approach - line by line parsing
console.log('\n4. Line by line approach:');
const lines = content.split('\n');
let inClass = false;
let currentClass = '';
let properties = [];

for (let i = 0; i < lines.length; i++) {
  const line = lines[i].trim();
  
  // Check for class start
  const classMatch = line.match(/export class (\w+)/);
  if (classMatch) {
    inClass = true;
    currentClass = classMatch[1];
    properties = [];
    console.log(`\nStarting class: ${currentClass}`);
    continue;
  }
  
  // Check for class end
  if (line === '}' && inClass) {
    console.log(`Ending class: ${currentClass}, found ${properties.length} properties`);
    properties.forEach(prop => {
      console.log(`  - ${prop.name}: ${prop.type} (nullable: ${prop.nullable})`);
    });
    inClass = false;
    continue;
  }
  
  // Look for @ApiProperty
  if (inClass && line.startsWith('@ApiProperty')) {
    // Parse the @ApiProperty config
    let configLine = line;
    let j = i;
    
    // Handle multiline @ApiProperty
    while (!configLine.includes(')') && j < lines.length - 1) {
      j++;
      configLine += ' ' + lines[j].trim();
    }
    
    // Find the property declaration (next non-decorator line)
    let propLine = '';
    j++;
    while (j < lines.length && (lines[j].trim().startsWith('@') || lines[j].trim() === '')) {
      j++;
    }
    
    if (j < lines.length) {
      propLine = lines[j].trim();
      
      // Extract property info
      const propMatch = propLine.match(/([a-zA-Z_$][a-zA-Z0-9_$]*)\s*[!:]?\s*([^;=\n]+)/);
      if (propMatch) {
        const propName = propMatch[1];
        const propType = propMatch[2];
        
        // Extract type from @ApiProperty
        const typeMatch = configLine.match(/type:\s*['"`]([^'"`]+)['"`]/);
        const nullableMatch = configLine.match(/nullable:\s*(true|false)/);
        
        properties.push({
          name: propName,
          type: typeMatch ? typeMatch[1] : 'unknown',
          tsType: propType,
          nullable: nullableMatch ? nullableMatch[1] === 'true' : false
        });
      }
    }
    
    i = j; // Skip to after the property
  }
}
