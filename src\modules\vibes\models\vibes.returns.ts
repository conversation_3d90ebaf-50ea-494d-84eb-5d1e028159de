import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { BasicAccountListItem } from 'src/models/basic-account-list-item';
import { VibeItem } from 'src/models/vibe-item';

export class CreateVibeReturn {
  @ApiProperty({ type: 'string' })
  vibeId!: string;
}

export class GetCommentsByVibeIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      type: 'object',
      properties: {
        commentId: { type: 'string' },
        comment: { type: 'string' },
        commenter: {
          $ref: getSchemaPath(BasicAccountListItem),
        },
        createdAt: { type: 'string' },
      },
    },
  })
  comments!: {
    commentId: string;
    comment: string;
    commenter: BasicAccountListItem;
    createdAt: string;
  }[];

  @ApiProperty({ type: 'number' })
  commentCount!: number;
}

export class GetLikesByVibeIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      allOf: [
        { $ref: getSchemaPath(BasicAccountListItem) },
        { type: 'object', properties: { isActionable: { type: 'boolean' } } },
      ],
    },
  })
  likes!: (BasicAccountListItem & { isActionable: boolean })[];

  @ApiProperty({ type: 'number' })
  likeCount!: number;
}

export class GetVibeByVibeIdReturn extends VibeItem {}

export class GetVibesReturn {
  @ApiProperty({
    type: 'array',
    items: {
      type: 'object',
      properties: {
        content: { $ref: getSchemaPath(VibeItem) },
        thumbnailUrl: { type: 'string' },
      },
    },
  })
  vibes!: {
    content: VibeItem;
    thumbnailUrl: string;
  }[];

  @ApiProperty({ type: 'number' })
  vibeCount!: number;
}
