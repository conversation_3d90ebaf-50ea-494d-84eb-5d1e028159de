// Generated Dart model for GetMemoryFoldersByUserIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getmemoryfoldersbyuseridreturn_model.freezed.dart';
part 'getmemoryfoldersbyuseridreturn_model.g.dart';

@freezed
class GetMemoryFoldersByUserIdReturnModel with _$GetMemoryFoldersByUserIdReturnModel {
  const factory GetMemoryFoldersByUserIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int memoryFolderCount,
  }) = _GetMemoryFoldersByUserIdReturnModel;

  factory GetMemoryFoldersByUserIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetMemoryFoldersByUserIdReturnModelFromJson(json);
}
