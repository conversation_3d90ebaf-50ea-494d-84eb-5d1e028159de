// Generated Dart model for GetVibesReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'getvibesreturn_model.freezed.dart';
part 'getvibesreturn_model.g.dart';

@freezed
class GetVibesReturnModel with _$GetVibesReturnModel {
  const factory GetVibesReturnModel({
    required List<Map<String, dynamic>> thumbnailUrl,
  }) = _GetVibesReturnModel;

  factory GetVibesReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetVibesReturnModelFromJson(json);
}
