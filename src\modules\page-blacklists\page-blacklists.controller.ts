import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { ApiResponseObject } from 'src/models/api-response-object';
import { EmptyReturn } from 'src/models/empty-return';
import { BlockUserByPageIdDto, UnblockUserByPageIdDto } from './models/page-blacklists.dto';
import { SearchPageBlocklistByPageIdReturn } from './models/page-blacklists.returns';
import { PageBlacklistsService } from './page-blacklists.service';

@ApiTags('pageBlacklists')
@Controller('pageBlacklists')
export class PageBlacklistsController {
  constructor(private readonly pageBlacklistsService: PageBlacklistsService) {}

  @ApiOperation({
    summary: 'Sayfa IDsi ile engellenenleri listeler',
  })
  @ApiResponseObject({
    model: SearchPageBlocklistByPageIdReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'number' })
  @ApiQuery({ name: 'page', required: false, type: 'number' })
  @Get(':pageId/blocklist')
  async searchPageBlocklistByPageId(
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pageBlacklistsService.searchPageBlocklistByPageId({
      sessionId,
      sessionRole,
      pageId,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile bir hesabı engeller',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':pageId/block')
  async blockUserByPageId(
    @Body() blockUserByPageIdDto: BlockUserByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pageBlacklistsService.blockUserByPageId({
      sessionId,
      sessionRole,
      pageId,
      ...blockUserByPageIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Sayfa IDsi ile bir hesabın engelini kaldırır',
  })
  @ApiResponseObject({
    model: EmptyReturn,
  })
  @Post(':pageId/unblock')
  async unblockUserByPageId(
    @Body() unblockUserByPageIdDto: UnblockUserByPageIdDto,
    @Res({ passthrough: true }) res: Response,
    @Param('pageId', new ParseUUIDPipe({ version: '4' })) pageId: string,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.pageBlacklistsService.unblockUserByPageId({
      sessionId,
      sessionRole,
      pageId,
      ...unblockUserByPageIdDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
