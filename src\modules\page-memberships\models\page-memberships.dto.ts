import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID } from 'class-validator';

export class AddPageMembersByPageIdDto {
  @ApiProperty({ type: 'array', items: { type: 'string' } })
  @IsArray()
  @IsUUID('4', { each: true })
  userIds!: string[];
}

export class RemovePageModeratorByPageIdDto {
  @ApiProperty({ type: 'string' })
  @IsUUID('4')
  userId!: string;
}

export class RemovePageMemberByPageIdDto {
  @ApiProperty({ type: 'string' })
  @IsArray()
  @IsUUID('4', { each: true })
  userId!: string;
}

export class TransferPageAdministrationByPageIdDto {
  @ApiProperty({ type: 'string' })
  @IsUUID('4')
  userId!: string;
}
