import {
  <PERSON><PERSON>n,
  <PERSON>reateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { IventCollab } from './ivent-collab.entity';
import { Ivent } from './ivent.entity';
import { Location } from './location.entity';
import { Memory } from './memory.entity';
import { PageBlacklist } from './page-blacklist.entity';
import { PageBlockByUser } from './page-block-by-user.entity';
import { PageFollower } from './page-follower.entity';
import { PageMembership } from './page-membership.entity';
import { PageSubscriber } from './page-subscriber.entity';
import { PageTag } from './page-tag.entity';
import { UserNotification } from './user-notification.entity';
import { User } from './user.entity';
import { Vibe } from './vibe.entity';
import { ActiveSessionIvents, CollabSummaryOfIvent, IventUsers } from './views';

@Entity('pages')
export class Page {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'varchar', length: 255 })
  page_name!: string;

  @Column({ type: 'text', nullable: true })
  thumbnail_url!: string | null;

  @Column({ type: 'text', nullable: true })
  website_url!: string | null;

  @Column({ type: 'text', nullable: true })
  description!: string | null;

  @Column({ type: 'boolean', default: false })
  is_edu!: boolean;

  @Column({ type: 'boolean', default: false })
  have_membership!: boolean;

  @Column({ type: 'uuid', nullable: true })
  location_id!: string | null;

  @ManyToOne(() => Location, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'location_id' })
  location?: Location | null;

  @Column({ type: 'uuid' })
  creator_id!: string;

  @ManyToOne(() => User, { onDelete: 'RESTRICT' })
  @JoinColumn({ name: 'creator_id' })
  creator?: User;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from entities
  @OneToMany(() => Ivent, (ivent) => ivent.creator_page)
  created_ivents?: Ivent[];

  @OneToMany(() => IventCollab, (iventCollab) => iventCollab.collab_page)
  ivent_collabs?: IventCollab[];

  @OneToMany(() => Memory, (memory) => memory.creator_page)
  created_memories?: Memory[];

  @OneToMany(() => Vibe, (vibe) => vibe.creator_page)
  created_vibes?: Vibe[];

  @OneToMany(() => PageMembership, (pageMembership) => pageMembership.page)
  memberships?: PageMembership[];

  @OneToMany(() => PageBlacklist, (pageBlacklist) => pageBlacklist.page)
  blacklists?: PageBlacklist[];

  @OneToMany(() => PageBlockByUser, (pageBlockByUser) => pageBlockByUser.page)
  page_blocks_by_users?: PageBlockByUser[];

  @OneToMany(() => PageFollower, (pageFollower) => pageFollower.page)
  followers?: PageFollower[];

  @OneToMany(() => PageSubscriber, (pageSubscriber) => pageSubscriber.page)
  subscribers?: PageSubscriber[];

  @OneToMany(() => PageTag, (pageTag) => pageTag.page)
  tags?: PageTag[];

  @OneToMany(() => UserNotification, (userNotification) => userNotification.page)
  user_notifications?: UserNotification[];

  // Additional relationships from views
  @OneToMany(() => IventUsers, (iventUsers) => iventUsers.page)
  ivent_users?: IventUsers[];

  @OneToMany(() => ActiveSessionIvents, (activeSessionIvents) => activeSessionIvents.page)
  active_session_ivents?: ActiveSessionIvents[];

  @OneToMany(() => CollabSummaryOfIvent, (collabSummaryOfIvent) => collabSummaryOfIvent.page)
  collab_summary_of_ivent?: CollabSummaryOfIvent[];
}
