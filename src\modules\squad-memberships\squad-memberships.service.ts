import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { EmptyReturn } from 'src/models/empty-return';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { DataSource } from 'typeorm';
import { NotificationsService } from '../notifications/notifications.service';
import { SquadsService } from '../squads/squads.service';
import {
  InviteFriendsByIventId,
  JoinIventAndCreateSquadByIventIdParams,
  LeaveSquadByIventId,
  SearchInvitableUsersByIventIdParams,
  SearchParticipantsByIventIdParams,
} from './models/squad-memberships.params';
import {
  SearchInvitableUsersByIventIdReturn,
  SearchParticipantsByIventIdReturn,
} from './models/squad-memberships.returns';

@Injectable()
export class SquadMembershipsService {
  constructor(
    private dataSource: DataSource,
    private notificationsService: NotificationsService,
    private squadsService: SquadsService,
  ) {}

  // Description: List 'user's and 'group's that can be invited to join to 'ivent'
  // ----------------------------------------------------------------------
  // 1 => The 'ivent' with the given 'ivent ID' must exist
  //
  // 2 => The 'session user' must be not participating in the 'ivent' as a 'collab'
  //
  // 3 => If the 'session user' belongs to a 'squad', the 'user's must be not invited to the same 'ivent' with the same 'squad'
  //
  // 4 => The 'user's must be not participating in the 'ivent' as a 'member' or 'collab'
  //
  // 5 => At least one of the 'group member's should be available to be invited by the criterias (3, 4) above
  //
  // 6 => The 'group's must be not recorded in 'ivent_group' table with the 'ivent ID'
  //
  async searchInvitableUsersByIventId(
    searchInvitableUsersByIventIdParams: SearchInvitableUsersByIventIdParams,
  ): Promise<SearchInvitableUsersByIventIdReturn> {
    const { sessionId, sessionRole, iventId, type, q, limit, page } =
      searchInvitableUsersByIventIdParams;

    const iventResult = await this.dataSource.query(
      `SELECT ivent_name, thumbnail_url FROM ivents WHERE id = '${iventId}'`,
    );
    if (iventResult.length === 0) {
      throw new HttpException('No ivent exists with given ivent ID', HttpStatus.BAD_REQUEST);
    }

    // Check if the session user is a member of a squad and get that squad's id
    const squadResult = await this.dataSource.query(`
      SELECT s.id AS squad_id
      FROM squad_memberships sm
      LEFT JOIN squads s ON s.id = sm.squad_id
      WHERE sm.member_id = '${sessionId}'
      AND sm.status IN ('accepted', 'joined')
      AND s.ivent_id = '${iventId}';
    `);
    const orClause = squadResult.length
      ? `(status = 'pending' AND id = '${squadResult[0].squad_id}')`
      : 'FALSE';
    const groupQClause = q ? `g.group_name ILIKE '${q}%'` : 'TRUE';
    const userQClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const result =
      type === 'group'
        ? await this.dataSource.query(`
      WITH ActiveIventGroups AS (
          -- Groups participated in the ivent
          SELECT group_id
          FROM ivent_groups
          WHERE ivent_id = '${iventId}'
      ),
      ActiveIventUsers AS (
          -- Users participated in the ivent or invited to the squad that the session user belongs to
          SELECT account_id AS member_id
          FROM ivent_users
          WHERE ivent_id = '${iventId}'
          AND (status IN ('accepted', 'admin', 'joined') OR ${orClause})
      ),
      MemberSummariesOfGroups AS (
          -- Summaries of their members of the groups
          SELECT
              CONCAT(ARRAY_TO_STRING((ARRAY_AGG(u.firstname))[:2], ','), '|', COUNT(u.id)) AS member_summary,
              gm.group_id AS group_id
          FROM group_memberships gm
          LEFT JOIN users u ON u.id = gm.member_id
          WHERE gm.status IN ('accepted', 'moderator', 'admin')
          AND gm.member_id != '${sessionId}'
          AND gm.group_id NOT IN (SELECT group_id FROM ActiveIventGroups)
          AND gm.member_id NOT IN (SELECT member_id FROM ActiveIventUsers)
          GROUP BY gm.group_id
      )
      SELECT
          g.id AS group_id,
          g.group_name AS group_name,
          g.thumbnail_url AS thumbnail_url,
          msog.member_summary AS member_summary
      FROM group_memberships gm
      LEFT JOIN groups g ON g.id = gm.group_id
      INNER JOIN MemberSummariesOfGroups msog ON msog.group_id = gm.group_id
      WHERE gm.member_id = '${sessionId}'
      AND msog.member_summary IS NOT NULL
      AND ${groupQClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `)
        : await this.dataSource.query(`
      WITH ActiveIventUsers AS (
          -- Users participated in the ivent or invited to the squad that the session user belongs to
          SELECT account_id AS member_id
          FROM ivent_users
          WHERE ivent_id = '${iventId}'
          AND (status IN ('accepted', 'admin', 'joined') OR ${orClause})
      )
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.firstname AS firstname,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM user_friendships uf
      LEFT JOIN users u ON u.id = uf.friend_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      WHERE uf.user_id = '${sessionId}'
      AND uf.friend_id NOT IN (SELECT member_id FROM ActiveIventUsers)
      AND ${userQClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return type === 'group'
      ? {
          groups: result.map((val) => ({
            groupId: val.group_id,
            groupName: val.group_name,
            thumbnailUrl: val.thumbnail_url,
            memberNames: val.member_summary ? val.member_summary.split('|')[0].split(',') : [],
            memberCount: val.member_summary ? Number(val.member_summary.split('|')[1]) : 0,
          })),
          groupCount: result.length,
          friends: [],
          friendCount: 0,
        }
      : {
          friends: result.map((val) => ({
            userId: val.user_id,
            username: val.username,
            firstname: val.firstname,
            avatarUrl: val.avatar_url,
            university: val.university,
          })),
          friendCount: result.length,
          groups: [],
          groupCount: 0,
        };
  }

  // Description: List 'user's that are related to the 'ivent'
  // ----------------------------------------------------------------------
  // 1 => The 'ivent' with the given 'ivent ID' must exist
  //
  async searchParticipantsByIventId(
    searchParticipantsByIventIdParams: SearchParticipantsByIventIdParams,
  ): Promise<SearchParticipantsByIventIdReturn> {
    const { sessionId, sessionRole, iventId, q, limit, page } = searchParticipantsByIventIdParams;

    const iventResult = await this.dataSource.query(
      `SELECT ivent_name, thumbnail_url FROM ivents WHERE id = '${iventId}'`,
    );
    if (iventResult.length === 0) {
      throw new HttpException('No ivent exists with given ivent ID', HttpStatus.BAD_REQUEST);
    }

    const viewTypeResult = await this.dataSource.query(`
      SELECT
          CASE
              WHEN type = 'member' THEN 'joined'
              WHEN type IN ('page', 'user') THEN 'created'
          END AS view_type
      FROM ivent_users
      WHERE account_id = '${sessionId}'
      AND ivent_id = '${iventId}'
      AND status IN ('accepted', 'joined', 'admin');
    `);
    const sessionViewType = viewTypeResult.length ? viewTypeResult[0].view_type : 'default';

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const query =
      sessionViewType === 'default'
        ? `
          SELECT
              u.id AS user_id,
              u.username AS username,
              u.avatar_url AS avatar_url,
              uni.university_name AS university,
              'joined' AS type
          FROM user_friendships
          INNER JOIN ivent_users iu ON iu.account_id = friend_id
          LEFT JOIN users u ON u.id = iu.account_id
          LEFT JOIN universities uni ON uni.university_code = u.university_code
          WHERE user_id = '${sessionId}'
          AND iu.type = 'member'
          AND iu.ivent_id = '${iventId}'
          AND iu.status IN ('accepted', 'joined')
          AND ${qClause}
          LIMIT ${limit}
          OFFSET ${limit * (page - 1)};
        `
        : sessionViewType === 'joined'
          ? `
          WITH RelationshipsOfSessionUser AS (
              -- Record of whether the session user is friend with the user listed
              SELECT
                  CASE
                      WHEN sender_id = '${sessionId}' THEN receiver_id
                      WHEN receiver_id = '${sessionId}' THEN sender_id
                  END AS friend_id,
                  status
              FROM user_relationships
              WHERE sender_id = '${sessionId}'
              OR receiver_id = '${sessionId}'
          )
          SELECT
              u.id AS user_id,
              u.username AS username,
              u.avatar_url AS avatar_url,
              uni.university_name AS university,
              rosu.status AS relationship
          FROM squad_friendships sf
          LEFT JOIN squads s ON s.id = sf.squad_id
          LEFT JOIN users u ON u.id = sf.other_member_id
          LEFT JOIN universities uni ON uni.university_code = u.university_code
          LEFT JOIN RelationshipsOfSessionUser rosu ON rosu.friend_id = u.id
          WHERE sf.member_id = '${sessionId}'
          AND s.ivent_id = '${iventId}'
          AND ${qClause}
          LIMIT ${limit}
          OFFSET ${limit * (page - 1)};
        `
          : `
          SELECT
              u.id AS user_id,
              u.username AS username,
              u.avatar_url AS avatar_url,
              uni.university_name AS university
          FROM ivent_users iu
          LEFT JOIN users u ON u.id = iu.account_id
          LEFT JOIN universities uni ON uni.university_code = u.university_code
          WHERE iu.type = 'member'
          AND ivent_id = '${iventId}'
          AND ${qClause}
          LIMIT ${limit}
          OFFSET ${limit * (page - 1)};
        `;

    const result = await this.dataSource.query(query);

    return sessionViewType === null
      ? {
          users: result.map((val) => ({
            userId: val.user_id,
            username: val.username,
            avatarUrl: val.avatar_url,
            university: val.university,
            type: val.type,
          })),
          userCount: result.length,
          viewType: 'default',
        }
      : sessionViewType === 'joined'
        ? {
            users: result.map((val) => ({
              userId: val.user_id,
              username: val.username,
              avatarUrl: val.avatar_url,
              university: val.university,
              relationship: val.relationship,
              type: val.type,
            })),
            userCount: result.length,
            viewType: sessionViewType,
          }
        : {
            users: result.map((val) => ({
              userId: val.user_id,
              username: val.username,
              avatarUrl: val.avatar_url,
              university: val.university,
              type: val.type,
            })),
            userCount: result.length,
            viewType: sessionViewType,
          };
  }

  async joinIventAndCreateSquadByIventId(
    joinIventAndCreateSquadByIventIdParams: JoinIventAndCreateSquadByIventIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, iventId, groupIds, userIds } =
      joinIventAndCreateSquadByIventIdParams;

    const sessionUserResult = await this.dataSource.query(
      `SELECT username, avatar_url FROM users WHERE id = '${sessionId}'`,
    );

    const iventResult = await this.dataSource.query(
      `SELECT ivent_name, thumbnail_url FROM ivents WHERE id = '${iventId}'`,
    );
    if (iventResult.length === 0) {
      throw new HttpException('No ivent exists with given ivent ID', HttpStatus.BAD_REQUEST);
    }

    // Check if the session user is a member of the ivent
    const participationResult = await this.dataSource.query(`
      SELECT account_id
      FROM ivent_users
      WHERE ivent_id = '${iventId}'
      AND status IN ('accepted', 'admin', 'joined')
      AND account_id = '${sessionId}';
    `);
    if (participationResult.length) {
      throw new HttpException('You already participate in the ivent', HttpStatus.BAD_REQUEST);
    }

    // Create a new squad with the given ivent id
    const squadId = await this.squadsService.createSquad({ creatorId: sessionId, iventId });

    // Gather the users of the given groups
    const groupMembersResult = groupIds.length
      ? await this.dataSource.query(`
          SELECT DISTINCT member_id
          FROM group_memberships
          WHERE group_id IN (${groupIds.map((val) => `'${val}'`).join(',')})
          AND status IN ('accepted', 'moderator', 'admin')
          AND member_id != '${sessionId}';
        `)
      : [];
    const groupMemberIds = groupMembersResult.map((val) => val.member_id);

    // Check for the users that is participating in the ivent, and later filter them
    const iventUsers = (
      await this.dataSource.query(`
      SELECT account_id
      FROM ivent_users
      WHERE ivent_id = '${iventId}'
      AND status IN ('accepted', 'admin', 'joined');
    `)
    ).map((val) => val.account_id);
    const availableUsers = Array.from(new Set([...userIds, ...groupMemberIds])).filter(
      (item) => !iventUsers.includes(item),
    );

    const squadMembershipInsertResult = await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'squad_memberships',
        values: [sessionId, ...availableUsers].map((val) => ({
          member_id: val,
          squad_id: squadId,
          status: val === sessionId ? 'joined' : 'accepted',
          inviter_id: sessionId,
        })),
      }),
    );

    // Create notifications if there are any available users
    squadMembershipInsertResult.forEach(async (val) => {
      if (val !== sessionId) {
        await this.notificationsService.sendNotifications(
          {
            notification_type: 'type_12',
            account_type: 'user',
            account_id: sessionId,
            account_name: sessionUserResult[0].username,
            account_image_url: sessionUserResult[0].avatar_url,
            content_type: 'ivent',
            content_id: iventId,
            content_name: iventResult[0].ivent_name,
            content_thumbnail_url: iventResult[0].thumbnail_url,
            subject_id: val.id,
          },
          [val.member_id],
        );
      }
    });

    return {};
  }

  async leaveSquadByIventId(leaveSquadByIventId: LeaveSquadByIventId): Promise<EmptyReturn> {
    const { sessionId, sessionRole, iventId } = leaveSquadByIventId;

    const iventResult = await this.dataSource.query(
      `SELECT ivent_name, thumbnail_url FROM ivents WHERE id = '${iventId}'`,
    );
    if (iventResult.length === 0) {
      throw new HttpException('No ivent exists with given ivent ID', HttpStatus.BAD_REQUEST);
    }

    const squadResult = await this.dataSource.query(`
      SELECT sm.id AS squad_membership_id
      FROM squad_memberships sm
      LEFT JOIN squads s ON s.id = sm.squad_id
      WHERE sm.member_id = '${sessionId}'
      AND sm.status IN ('accepted', 'joined')
      AND s.ivent_id = '${iventId}';
    `);
    if (squadResult.length === 0) {
      throw new HttpException(
        'No squad exist with given user ID and ivent ID',
        HttpStatus.BAD_REQUEST,
      );
    }
    const squadMembershipId = squadResult[0].squad_membership_id;

    await this.dataSource
      .createQueryBuilder()
      .update('squad_memberships')
      .set({
        status: 'left',
      })
      .where({
        id: squadMembershipId,
      })
      .execute();

    return {};
  }

  async inviteFriendsByIventId(
    inviteFriendsByIventId: InviteFriendsByIventId,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, iventId, userIds, groupIds } = inviteFriendsByIventId;

    const sessionUserResult = await this.dataSource.query(
      `SELECT username, avatar_url FROM users WHERE id = '${sessionId}'`,
    );

    const iventResult = await this.dataSource.query(
      `SELECT ivent_name, thumbnail_url FROM ivents WHERE id = '${iventId}'`,
    );
    if (iventResult.length === 0) {
      throw new HttpException('No ivent exists with given ivent ID', HttpStatus.BAD_REQUEST);
    }

    // Check if the session user is a member of a squad and get that squad's id
    const squadResult = await this.dataSource.query(`
      SELECT s.id AS squad_id
      FROM squad_memberships sm
      LEFT JOIN squads s ON s.id = sm.squad_id
      WHERE member_id = '${sessionId}'
      AND s.ivent_id = '${iventId}'
      AND sm.status IN ('accepted', 'joined');
    `);
    if (squadResult.length === 0) {
      throw new HttpException(
        'You do not belong to any squad with given ivent ID',
        HttpStatus.BAD_REQUEST,
      );
    }
    const squadId = squadResult[0].squad_id;

    // Gather the users of the given groups
    const groupMembersResult = groupIds.length
      ? await this.dataSource.query(`
          SELECT DISTINCT member_id
          FROM group_memberships
          WHERE group_id IN (${groupIds.map((val) => `'${val}'`).join(',')})
          AND status IN ('accepted', 'moderator', 'admin')
          AND member_id != '${sessionId}';
        `)
      : [];
    const groupMemberIds = groupMembersResult.map((val) => val.member_id);

    // Check for the users that is participating in the ivent OR invited to the squad, and later filter them
    const iventUsersResult = (
      await this.dataSource.query(`
      SELECT account_id
      FROM ivent_users
      WHERE ivent_id = '${iventId}'
      AND (status IN ('accepted', 'admin', 'joined') OR (status = 'pending' AND id = '${squadId}'));
    `)
    ).map((val) => val.account_id);
    const availableUsers = Array.from(new Set([...userIds, ...groupMemberIds])).filter(
      (item) => !iventUsersResult.includes(item),
    );

    const squadMembershipInsertResult = await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'squad_memberships',
        values: availableUsers.map((val) => ({
          member_id: val,
          squad_id: squadId,
          status: 'pending',
          inviter_id: sessionId,
        })),
      }),
    );

    // Create notifications if there are any available users
    squadMembershipInsertResult.forEach(async (val) => {
      await this.notificationsService.sendNotifications(
        {
          notification_type: 'type_12',
          account_type: 'user',
          account_id: sessionId,
          account_name: sessionUserResult[0].username,
          account_image_url: sessionUserResult[0].avatar_url,
          content_type: 'ivent',
          content_id: iventId,
          content_name: iventResult[0].ivent_name,
          content_thumbnail_url: iventResult[0].thumbnail_url,
          subject_id: val.id,
        },
        [val.member_id],
      );
    });

    return {};
  }
}
