// Generated Dart model for SendVerificationEmailDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'sendverificationemaildto_model.freezed.dart';
part 'sendverificationemaildto_model.g.dart';

@freezed
class SendVerificationEmailDtoModel with _$SendVerificationEmailDtoModel {
  const factory SendVerificationEmailDtoModel({
    required String newUsername,
    required String newBirthday,
    required String newGender,
    required String newAvatarUrl,
    required String newEmail,
    required String newGrad,
    required String newPhoneNumber,
  }) = _SendVerificationEmailDtoModel;

  factory SendVerificationEmailDtoModel.fromJson(Map<String, dynamic> json) =>
      _$SendVerificationEmailDtoModelFromJson(json);
}
