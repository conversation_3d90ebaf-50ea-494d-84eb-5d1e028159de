// Generated Dart model for SearchParticipantsByIventIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchparticipantsbyiventidreturn_model.freezed.dart';
part 'searchparticipantsbyiventidreturn_model.g.dart';

@freezed
class SearchParticipantsByIventIdReturnModel with _$SearchParticipantsByIventIdReturnModel {
  const factory SearchParticipantsByIventIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int userCount,
    required String viewType,
  }) = _SearchParticipantsByIventIdReturnModel;

  factory SearchParticipantsByIventIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchParticipantsByIventIdReturnModelFromJson(json);
}
