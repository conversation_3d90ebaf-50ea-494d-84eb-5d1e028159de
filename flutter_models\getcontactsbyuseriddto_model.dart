// Generated Dart model for GetContactsByUserIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'getcontactsbyuseriddto_model.freezed.dart';
part 'getcontactsbyuseriddto_model.g.dart';

@freezed
class GetContactsByUserIdDtoModel with _$GetContactsByUserIdDtoModel {
  const factory GetContactsByUserIdDtoModel({
    required List<String> phoneNumbers,
  }) = _GetContactsByUserIdDtoModel;

  factory GetContactsByUserIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$GetContactsByUserIdDtoModelFromJson(json);
}
