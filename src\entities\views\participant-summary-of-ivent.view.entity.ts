import { stringToArrayTransformer, stringToNumberTransformer } from 'src/utils/value-transformers';
import { Join<PERSON><PERSON>umn, OneToOne, ViewColumn, ViewEntity } from 'typeorm';
import { Ivent } from '../ivent.entity';
import { IventUsers } from './ivent-users.view.entity';

@ViewEntity({
  name: 'participant_summary_of_ivent',
  expression: `
    SELECT
        iu.ivent_id,
        count(u.id) AS participant_count,
        array_to_string(
            (array_agg(u.firstname)) [:2],
            ','
        ) AS participant_first_names,
        array_to_string(
            (array_agg(u.avatar_url)) [:5],
            ','
        ) AS participant_avatar_urls
    FROM ivent_users iu
        LEFT JOIN users u ON u.id = iu.account_id
    WHERE
        iu.type = 'member'
        AND iu.status IN ('accepted', 'joined')
    GROUP BY
        iu.ivent_id
  `,
  dependsOn: [() => IventUsers],
})
export class ParticipantSummaryOfIvent {
  @ViewColumn()
  ivent_id!: string;

  @OneToOne(() => Ivent)
  @JoinColumn({ name: 'ivent_id' })
  ivent?: Ivent;

  @ViewColumn({ transformer: stringToNumberTransformer })
  participant_count!: number;

  @ViewColumn({ transformer: stringToArrayTransformer })
  participant_first_names!: string[];

  @ViewColumn({ transformer: stringToArrayTransformer })
  participant_avatar_urls!: (string | null)[];
}
