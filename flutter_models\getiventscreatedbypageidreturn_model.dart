// Generated Dart model for GetIventsCreatedByPageIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getiventscreatedbypageidreturn_model.freezed.dart';
part 'getiventscreatedbypageidreturn_model.g.dart';

@freezed
class GetIventsCreatedByPageIdReturnModel with _$GetIventsCreatedByPageIdReturnModel {
  const factory GetIventsCreatedByPageIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,
  }) = _GetIventsCreatedByPageIdReturnModel;

  factory GetIventsCreatedByPageIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetIventsCreatedByPageIdReturnModelFromJson(json);
}
