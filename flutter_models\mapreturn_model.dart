// Generated Dart model for MapReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'mapreturn_model.freezed.dart';
part 'mapreturn_model.g.dart';

@freezed
class MapReturnModel with _$MapReturnModel {
  const factory MapReturnModel({
    required List<Map<String, dynamic>> ivents,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,
  }) = _MapReturnModel;

  factory MapReturnModel.fromJson(Map<String, dynamic> json) =>
      _$MapReturnModelFromJson(json);
}
