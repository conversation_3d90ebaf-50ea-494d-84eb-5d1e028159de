// Generated Dart model for GetSuggestedImagesReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getsuggestedimagesreturn_model.freezed.dart';
part 'getsuggestedimagesreturn_model.g.dart';

@freezed
class GetSuggestedImagesReturnModel with _$GetSuggestedImagesReturnModel {
  const factory GetSuggestedImagesReturnModel({
    required List<Map<String, dynamic>> images,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int imageCount,
  }) = _GetSuggestedImagesReturnModel;

  factory GetSuggestedImagesReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetSuggestedImagesReturnModelFromJson(json);
}
