// Generated Dart model for BlockUserByPageIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'blockuserbypageiddto_model.freezed.dart';
part 'blockuserbypageiddto_model.g.dart';

@freezed
class BlockUserByPageIdDtoModel with _$BlockUserByPageIdDtoModel {
  const factory BlockUserByPageIdDtoModel({
    required String userId,
  }) = _BlockUserByPageIdDtoModel;

  factory BlockUserByPageIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$BlockUserByPageIdDtoModelFromJson(json);
}
