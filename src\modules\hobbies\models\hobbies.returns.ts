import { ApiProperty } from '@nestjs/swagger';

export class SearchH<PERSON><PERSON>Return {
  @ApiProperty({
    type: 'array',
    items: {
      type: 'object',
      example: [
        {
          Art: [
            {
              hobbyId: 'uuid',
              hobbyName: 'Paint',
              isAdded: true,
            },
          ],
          Music: [
            {
              hobbyId: 'uuid',
              hobbyName: 'Rock',
              isAdded: false,
            },
          ],
        },
      ],
      additionalProperties: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            hobbyId: { type: 'string' },
            hobbyName: { type: 'string' },
            isAdded: { type: 'boolean' },
          },
        },
      },
    },
  })
  hobbies!: Record<
    string,
    {
      hobbyId: string;
      hobbyName: string;
      isAdded: boolean;
    }[]
  >;

  @ApiProperty({ type: 'number', example: 2 })
  hobbyCount!: number;
}
