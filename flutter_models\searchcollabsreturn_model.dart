// Generated Dart model for SearchCollabsReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchcollabsreturn_model.freezed.dart';
part 'searchcollabsreturn_model.g.dart';

@freezed
class SearchCollabsReturnModel with _$SearchCollabsReturnModel {
  const factory SearchCollabsReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int collabCount,
  }) = _SearchCollabsReturnModel;

  factory SearchCollabsReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchCollabsReturnModelFromJson(json);
}
