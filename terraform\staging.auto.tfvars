#TERRAFORM VARIABLES
aws_prefix              = "ivent"
aws_project_name        = "ivent-api"
aws_ecr_repo_name       = "ivent_ecr_repo_staging"
aws_ecs_cluster_name    = "ivent-cluster-staging"
aws_rds_identifier_name = "ivent-rds-staging"
aws_rds_parameter_group = "default.postgres16"
aws_ecs_desired_count   = 1
aws_region              = "eu-central-1"
aws_profile             = "IVENT"
aws_ssl_domain          = "*.ivent.app"
aws_root_domain         = "ivent.app."

#APP VARIABLES
app_env           = "staging"
app_domain        = "ivent-staging-api.ivent.app"
app_port          = "3000"
database_name     = "ivent-test"
database_user     = "postgres"
database_password = "[IVent]2024"
database_port     = "5432"
jwt_secret        = "1v3n7"


#S3 BUCKET 
s3_access_key = ""
s3_secret_key  = ""
s3_region     = "eu-central-1"
