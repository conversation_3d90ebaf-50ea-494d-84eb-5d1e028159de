// Generated Dart model for CreateIventDto
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'createiventdto_model.freezed.dart';
part 'createiventdto_model.g.dart';

@freezed
class CreateIventDtoModel with _$CreateIventDtoModel {
  const factory CreateIventDtoModel({
    required String creatorType,
    required String iventName,
    String? thumbnailUrl,
    String? thumbnailBuffer,
    required String dates,
    String? locationId,
    String? mapboxId,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int latitude,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int longitude,
    required String locationName,
    String? openAddress,
    String? description,
    required String categoryTagId,
    required List<String> tags,
    required String privacy,
    required List<String> allowedUniversityCodes,
    required List<String> collabs,
    String? googleFormsUrl,
    String? instagramUsername,
    String? whatsappUrl,
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    bool? isWhatsappUrlPrivate,
    String? whatsappNumber,
    String? callNumber,
    String? websiteUrl,
    required List<String> newDates,
    required String newDetails,
    required String newlocationId,
    required List<String> iventIds,
  }) = _CreateIventDtoModel;

  factory CreateIventDtoModel.fromJson(Map<String, dynamic> json) =>
      _$CreateIventDtoModelFromJson(json);
}
