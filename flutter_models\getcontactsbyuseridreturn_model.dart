// Generated Dart model for GetContactsByUserIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getcontactsbyuseridreturn_model.freezed.dart';
part 'getcontactsbyuseridreturn_model.g.dart';

@freezed
class GetContactsByUserIdReturnModel with _$GetContactsByUserIdReturnModel {
  const factory GetContactsByUserIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int contactCount,
  }) = _GetContactsByUserIdReturnModel;

  factory GetContactsByUserIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetContactsByUserIdReturnModelFromJson(json);
}
