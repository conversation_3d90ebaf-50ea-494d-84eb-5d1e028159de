// Generated Dart model for GetVibeByVibeIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'getvibebyvibeidreturn_model.freezed.dart';
part 'getvibebyvibeidreturn_model.g.dart';

@freezed
class GetVibeByVibeIdReturnModel with _$GetVibeByVibeIdReturnModel {
  const factory GetVibeByVibeIdReturnModel({
    required List<Map<String, dynamic>> thumbnailUrl,
  }) = _GetVibeByVibeIdReturnModel;

  factory GetVibeByVibeIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetVibeByVibeIdReturnModelFromJson(json);
}
