// Generated Dart model for GetLocationsReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getlocationsreturn_model.freezed.dart';
part 'getlocationsreturn_model.g.dart';

@freezed
class GetLocationsReturnModel with _$GetLocationsReturnModel {
  const factory GetLocationsReturnModel({
    required List<Map<String, dynamic>> locations,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int locationCount,
  }) = _GetLocationsReturnModel;

  factory GetLocationsReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetLocationsReturnModelFromJson(json);
}
