import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { BasicAccountListItem } from 'src/models/basic-account-list-item';

export class SearchCollabsForIventCreationReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(BasicAccountListItem),
    },
  })
  accounts!: BasicAccountListItem[];

  @ApiProperty({ type: 'number' })
  accountCount!: number;
}

export class SearchCollabsReturn {
  @ApiProperty({
    type: 'array',
    items: {
      allOf: [
        { $ref: getSchemaPath(BasicAccountListItem) },
        {
          type: 'object',
          properties: {
            membershipStatus: { type: 'string', nullable: true },
            friendshipStatus: { type: 'string', nullable: true },
          },
        },
      ],
    },
  })
  collabs!: (BasicAccountListItem & { membershipStatus: string | null; friendshipStatus: string | null })[];

  @ApiProperty({ type: 'number' })
  collabCount!: number;
}
