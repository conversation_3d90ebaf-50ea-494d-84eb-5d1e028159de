export enum NotificationEnum {
  TYPE_1 = 'type_1',
  TYPE_2 = 'type_2',
  TYPE_3 = 'type_3',
  TYPE_4 = 'type_4',
  TYPE_5 = 'type_5',
  TYPE_6 = 'type_6',
  TYPE_7 = 'type_7',
  TYPE_8 = 'type_8',
  TYPE_9 = 'type_9',
  TYPE_10 = 'type_10',
  TYPE_11 = 'type_11',
  TYPE_12 = 'type_12',
  TYPE_13 = 'type_13',
  TYPE_14 = 'type_14',
  TYPE_15 = 'type_15',
  TYPE_16 = 'type_16',
  TYPE_17 = 'type_17',
  TYPE_18 = 'type_18',
  TYPE_19 = 'type_19',
  TYPE_20 = 'type_20',
  TYPE_21 = 'type_21',
  TYPE_22 = 'type_22',
  TYPE_23 = 'type_23',
  TYPE_24 = 'type_24',
  TYPE_25 = 'type_25',
  TYPE_26 = 'type_26',
  TYPE_27 = 'type_27',
  TYPE_28 = 'type_28',
  TYPE_29 = 'type_29',
  TYPE_30 = 'type_30',
  TYPE_31 = 'type_31',
  TYPE_32 = 'type_32',
  TYPE_33 = 'type_33',
  TYPE_34 = 'type_34',
}
