// Generated Dart model for SearchGroupMembersByGroupIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchgroupmembersbygroupidreturn_model.freezed.dart';
part 'searchgroupmembersbygroupidreturn_model.g.dart';

@freezed
class SearchGroupMembersByGroupIdReturnModel with _$SearchGroupMembersByGroupIdReturnModel {
  const factory SearchGroupMembersByGroupIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int memberCount,
  }) = _SearchGroupMembersByGroupIdReturnModel;

  factory SearchGroupMembersByGroupIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchGroupMembersByGroupIdReturnModelFromJson(json);
}
