// Generated Dart model for SendCreatorRequestFormDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'sendcreatorrequestformdto_model.freezed.dart';
part 'sendcreatorrequestformdto_model.g.dart';

@freezed
class SendCreatorRequestFormDtoModel with _$SendCreatorRequestFormDtoModel {
  const factory SendCreatorRequestFormDtoModel({
    required String newUsername,
    required String newBirthday,
    required String newGender,
    required String newAvatarUrl,
    required String newEmail,
    required String newGrad,
    required String newPhoneNumber,
  }) = _SendCreatorRequestFormDtoModel;

  factory SendCreatorRequestFormDtoModel.fromJson(Map<String, dynamic> json) =>
      _$SendCreatorRequestFormDtoModelFromJson(json);
}
