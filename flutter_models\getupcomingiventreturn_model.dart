// Generated Dart model for GetUpcomingIventReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getupcomingiventreturn_model.freezed.dart';
part 'getupcomingiventreturn_model.g.dart';

@freezed
class GetUpcomingIventReturnModel with _$GetUpcomingIventReturnModel {
  const factory GetUpcomingIventReturnModel({
    required String iventId,
    required String iventName,
    required String date,
    required List<String> memberNames,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int memberCount,
  }) = _GetUpcomingIventReturnModel;

  factory GetUpcomingIventReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetUpcomingIventReturnModelFromJson(json);
}
