// Generated Dart model for GetUserBannerByUserIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'getuserbannerbyuseridreturn_model.freezed.dart';
part 'getuserbannerbyuseridreturn_model.g.dart';

@freezed
class GetUserBannerByUserIdReturnModel with _$GetUserBannerByUserIdReturnModel {
  const factory GetUserBannerByUserIdReturnModel({
    required String userId,
    required String username,
    String? avatarUrl,
    required String fullname,
  }) = _GetUserBannerByUserIdReturnModel;

  factory GetUserBannerByUserIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetUserBannerByUserIdReturnModelFromJson(json);
}
