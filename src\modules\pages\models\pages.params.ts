export type BlockPageByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
};

export type CreatePageParams = {
  sessionId: string;
  sessionRole: string;
  pageName: string;
  thumbnailUrl: string | null;
  websiteUrl: string | null;
  description: string | null;
  isEdu: boolean;
  haveMembership: boolean;
  tags: string[];
  creatorIds: string[];
  locationId: string;
};

export type DeletePageByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
};

export type FollowByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
};

export type GetIventsCreatedByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
  limit: number;
  page: number;
};

export type GetPageByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
};

export type GetPageDetailsByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
};

export type GetVibeFoldersByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
  limit: number;
  page: number;
};

export type RemoveFollowerByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
  userId: string;
};

export type SearchFollowersByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
  q: string;
  limit: number;
  page: number;
};

export type SubscribeByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
};

export type UnblockPageByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
};

export type UnfollowByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
};

export type UnsubscribeByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
};

export type UpdateDescriptionByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
  newDescription: string;
};

export type UpdateLinksByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
  newLink: string;
};

export type UpdateLocationByPageIdParams = {
  sessionId: string;
  sessionRole: string;
  pageId: string;
  newLocationId: string;
};
