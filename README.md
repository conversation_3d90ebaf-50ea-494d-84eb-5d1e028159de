## ECS & Docker

### Get Credentials

aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin 093952231228.dkr.ecr.eu-central-1.amazonaws.com/ivent_ecr_repo_staging

### Build Container Image

docker build -t ivent_ecr_repo_staging .

### Tag Image

docker tag ivent_ecr_repo_staging:latest 093952231228.dkr.ecr.eu-central-1.amazonaws.com/ivent_ecr_repo_staging

### Push

docker push 093952231228.dkr.ecr.eu-central-1.amazonaws.com/ivent_ecr_repo_staging


## Terraform Scripts

requires AWS credentials (export AWS_PROFILE=IVENT)

➜  ivent-api git:(terraform) terraform workspace list                
➜  ivent-api git:(terraform) terraform workspace select staging      
➜  ivent-api git:(terraform) terraform plan -var-file=staging.tfvars 
➜  ivent-api git:(terraform) terraform apply -var-file=staging.tfvars




