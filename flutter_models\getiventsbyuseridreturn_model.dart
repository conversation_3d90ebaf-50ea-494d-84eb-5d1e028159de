// Generated Dart model for GetIventsByUserIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getiventsbyuseridreturn_model.freezed.dart';
part 'getiventsbyuseridreturn_model.g.dart';

@freezed
class GetIventsByUserIdReturnModel with _$GetIventsByUserIdReturnModel {
  const factory GetIventsByUserIdReturnModel({
    required List<String> ivents,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFirstPerson,
  }) = _GetIventsByUserIdReturnModel;

  factory GetIventsByUserIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetIventsByUserIdReturnModelFromJson(json);
}
