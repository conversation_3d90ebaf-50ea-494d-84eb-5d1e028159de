// Generated Dart model for GetVibeFoldersByUserIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getvibefoldersbyuseridreturn_model.freezed.dart';
part 'getvibefoldersbyuseridreturn_model.g.dart';

@freezed
class GetVibeFoldersByUserIdReturnModel with _$GetVibeFoldersByUserIdReturnModel {
  const factory GetVibeFoldersByUserIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int vibeFolderCount,
  }) = _GetVibeFoldersByUserIdReturnModel;

  factory GetVibeFoldersByUserIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetVibeFoldersByUserIdReturnModelFromJson(json);
}
