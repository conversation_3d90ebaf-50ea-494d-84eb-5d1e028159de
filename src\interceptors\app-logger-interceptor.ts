import { CallHand<PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class AppLoggerInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url } = request;
    const baseUrl = url.split('?')[0];
    const timestamp = new Date().toISOString();

    if (baseUrl !== '/health') {
      console.log(`[${timestamp}] 🔸 HANDLER START ${method} ${baseUrl}`);

      const startTime = Date.now();
      return next.handle().pipe(
        tap((data) => {
          const endTime = Date.now();
          const duration = endTime - startTime;
          const responseTimestamp = new Date().toISOString();
          
          console.log(`[${responseTimestamp}] 🔸 HANDLER COMPLETE ${method} ${baseUrl}`);
          console.log(`  Duration: ${duration}ms`);
          console.log(`  Response: ${JSON.stringify(data, null, 2)}`);
        }),
      );
    } else {
      console.log(`[${timestamp}] 🔸 HEALTH CHECK HANDLER`);
      return next.handle();
    }
  }
}
