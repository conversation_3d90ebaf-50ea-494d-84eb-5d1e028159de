export function insertQueryBuilder(options: {
  tableName: string;
  values: Record<string, any> | Record<string, any>[];
  onConflict?: 'DO NOTHING' | 'DO UPDATE';
  outOfString?: string[];
}): string {
  if (options.outOfString === undefined || options.outOfString === null) {
    options.outOfString = [];
  }

  if (Array.isArray(options.values)) {
    return options.values.length !== 0
      ? `INSERT INTO ${options.tableName} (${Object.keys(options.values[0]).join(', ')}) VALUES ${options.values
          .map(
            (val) =>
              `(${Object.entries(val)
                .map((sub_val) =>
                  sub_val[1] !== null && sub_val[1] !== undefined
                    ? options.outOfString!.includes(sub_val[0])
                      ? `${sub_val[1]}`
                      : `'${sub_val[1]}'`
                    : 'DEFAULT',
                )
                .join(', ')})`,
          )
          .join(',\n')}${
          options.onConflict ? `\nON CONFLICT (${Object.keys(options.values[0]).join(', ')}) ${options.onConflict}` : ''
        }\nRETURNING *;`
      : `SELECT NULL`;
  }
  return Object.keys(options.values).length !== 0
    ? `INSERT INTO ${options.tableName} (${Object.keys(options.values).join(', ')}) VALUES (${Object.entries(
        options.values,
      )
        .map((val) =>
          val[1] !== null && val[1] !== undefined
            ? options.outOfString!.includes(val[0])
              ? `${val[1]}`
              : `'${val[1]}'`
            : 'DEFAULT',
        )
        .join(', ')})${
        options.onConflict ? `\nON CONFLICT (${Object.keys(options.values).join(', ')}) ${options.onConflict}` : ''
      }\nRETURNING *;`
    : `INSERT INTO ${options.tableName} DEFAULT VALUES RETURNING *;`;
}
