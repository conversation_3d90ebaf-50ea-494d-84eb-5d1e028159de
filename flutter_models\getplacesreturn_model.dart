// Generated Dart model for GetPlacesReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getplacesreturn_model.freezed.dart';
part 'getplacesreturn_model.g.dart';

@freezed
class GetPlacesReturnModel with _$GetPlacesReturnModel {
  const factory GetPlacesReturnModel({
    required List<Map<String, dynamic>> places,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int placeCount,
  }) = _GetPlacesReturnModel;

  factory GetPlacesReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetPlacesReturnModelFromJson(json);
}
