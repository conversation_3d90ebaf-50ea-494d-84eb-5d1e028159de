// Generated Dart model for SearchInvitableUsersByGroupIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchinvitableusersbygroupidreturn_model.freezed.dart';
part 'searchinvitableusersbygroupidreturn_model.g.dart';

@freezed
class SearchInvitableUsersByGroupIdReturnModel with _$SearchInvitableUsersByGroupIdReturnModel {
  const factory SearchInvitableUsersByGroupIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int userCount,
  }) = _SearchInvitableUsersByGroupIdReturnModel;

  factory SearchInvitableUsersByGroupIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchInvitableUsersByGroupIdReturnModelFromJson(json);
}
