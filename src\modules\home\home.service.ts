import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import {
  FeedParams,
  MapParams,
  SearchAccountParams,
  SearchIventParams,
} from './models/home.params';
import {
  FeedReturn,
  MapReturn,
  SearchAccountReturn,
  SearchIventReturn,
} from './models/home.returns';

@Injectable()
export class HomeService {
  constructor(private dataSource: DataSource) {}

  private getDateRange(
    dateType: string,
    startDate?: string,
    endDate?: string,
  ): { startDateAdjusted: string; endDateAdjusted: string } {
    let startDateAdjusted;
    let endDateAdjusted;

    switch (dateType) {
      case 'today':
        startDateAdjusted = new Date().toISOString().split('T')[0];
        endDateAdjusted = new Date().toISOString().split('T')[0] + 'T23:59:59.999Z';
        break;
      case 'tomorrow':
        startDateAdjusted = new Date(new Date().setDate(new Date().getDate() + 1))
          .toISOString()
          .split('T')[0];
        endDateAdjusted =
          new Date(new Date().setDate(new Date().getDate() + 1)).toISOString().split('T')[0] +
          'T23:59:59.999Z';
        break;
      case 'this_week':
        startDateAdjusted = new Date(
          new Date().setDate(new Date().getDate() - new Date().getDay() + 1),
        )
          .toISOString()
          .split('T')[0];
        endDateAdjusted =
          new Date(new Date().setDate(new Date().getDate() + (6 - new Date().getDay() + 1)))
            .toISOString()
            .split('T')[0] + 'T23:59:59.999Z';
        break;
      case 'next_week':
        startDateAdjusted = new Date(
          new Date().setDate(new Date().getDate() + (7 - new Date().getDay() + 1)),
        )
          .toISOString()
          .split('T')[0];
        endDateAdjusted =
          new Date(new Date().setDate(new Date().getDate() + (13 - new Date().getDay() + 1)))
            .toISOString()
            .split('T')[0] + 'T23:59:59.999Z';
        break;
      case 'this_month':
        startDateAdjusted = new Date(new Date().setDate(1)).toISOString().split('T')[0];
        endDateAdjusted =
          new Date(new Date().setDate(31)).toISOString().split('T')[0] + 'T23:59:59.999Z';
        break;
      case 'this_summer':
        startDateAdjusted = new Date(new Date().setMonth(5, 1)).toISOString().split('T')[0];
        endDateAdjusted =
          new Date(new Date().setMonth(7, 31)).toISOString().split('T')[0] + 'T23:59:59.999Z';
        break;
      case 'holiday':
        startDateAdjusted = new Date(new Date().setMonth(11, 23)).toISOString().split('T')[0];
        endDateAdjusted =
          new Date(new Date().setMonth(11, 31)).toISOString().split('T')[0] + 'T23:59:59.999Z';
        break;
      case 'range':
        startDateAdjusted = startDate ? startDate.split('T')[0] : undefined;
        endDateAdjusted = endDate ? endDate.split('T')[0] + 'T23:59:59.999Z' : undefined;
        break;
      default:
        startDateAdjusted = undefined;
        endDateAdjusted = undefined;
        break;
    }

    return { startDateAdjusted, endDateAdjusted };
  }

  async feed(feedParams: FeedParams): Promise<FeedReturn> {
    const {
      sessionId,
      sessionRole,
      dateType,
      startDate,
      endDate,
      categories,
      locationCoeff,
      latStart,
      latEnd,
      lngStart,
      lngEnd,
      q,
      limit,
      page,
    } = feedParams;

    const { startDateAdjusted, endDateAdjusted } = this.getDateRange(dateType, startDate, endDate);

    const qClause = q ? `i.ivent_name ILIKE '${q}%' OR l.location_name ILIKE '${q}%'` : 'TRUE';
    // const categoriesClause = categories.length
    //   ? `AND i.id IN (SELECT ivent_id FROM ivent_tags WHERE hobby_id IN ('${categories.join("', '")}'))`
    //   : '';
    const categoriesClause = categories.length
      ? `AND i.category_tag_id IN ('${categories.join("', '")}')`
      : '';
    const dateClause =
      startDateAdjusted && endDateAdjusted
        ? `AND (d.ivent_date::date BETWEEN '${startDateAdjusted}' AND '${endDateAdjusted}')`
        : '';
    const geomClause =
      lngStart && lngEnd && latStart && latEnd
        ? `AND ST_Within(l.geom::geometry, ST_MakeEnvelope(${latStart}, ${lngStart}, ${latEnd}, ${lngEnd}, 4326)::geometry)`
        : '';

    const query = sessionId
      ? `
      SELECT
          i.id AS ivent_id,
          i.ivent_name AS ivent_name,
          i.thumbnail_url AS thumbnail_url,
          l.location_name AS location_name,
          i.creator_type AS creator_type,
          COALESCE(p.id, u.id, dis.id) AS creator_id,
          COALESCE(p.page_name, u.username, dis.distributor_name) AS creator_name,
          COALESCE(p.thumbnail_url, u.avatar_url, dis.thumbnail_url) AS creator_image_url,
          'default' AS view_type,
          EXISTS (
              SELECT 1
              FROM user_favorites
              WHERE
                  user_id = '${sessionId}'
                  AND favorited_ivent_id = i.id
          ) AS is_favorited
      FROM ivents i
      LEFT JOIN ivent_dates d ON d.ivent_id = i.id
      LEFT JOIN locations l ON l.id = i.location_id
      LEFT JOIN pages p ON p.id = i.creator_page_id
      LEFT JOIN users u ON u.id = i.creator_user_id
      LEFT JOIN distributors dis ON dis.id = i.creator_distributor_id
      WHERE ${qClause}
      ${categoriesClause}
      ${dateClause}
      ${geomClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `
      : `
      SELECT
          i.id AS ivent_id,
          i.ivent_name AS ivent_name,
          i.thumbnail_url AS thumbnail_url,
          l.location_name AS location_name,
          i.creator_type AS creator_type,
          COALESCE(p.id, u.id, dis.id) AS creator_id,
          COALESCE(p.page_name, u.username, dis.distributor_name) AS creator_name,
          COALESCE(p.thumbnail_url, u.avatar_url, dis.thumbnail_url) AS creator_image_url,
          'viewer' AS view_type
      FROM ivents i
      LEFT JOIN ivent_dates d ON d.ivent_id = i.id
      LEFT JOIN locations l ON l.id = i.location_id
      LEFT JOIN pages p ON p.id = i.creator_page_id
      LEFT JOIN users u ON u.id = i.creator_user_id
      LEFT JOIN distributors dis ON dis.id = i.creator_distributor_id
      WHERE ${qClause}
      ${categoriesClause}
      ${dateClause}
      ${geomClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `;

    const queryResult = await this.dataSource.query(query);
    return {
      ivents: queryResult.map((val) => {
        const defaultReturn = {
          iventId: val.ivent_id,
          iventName: val.ivent_name,
          thumbnailUrl: val.thumbnail_url,
          locationName: val.location_name,
          creatorId: val.creator_id,
          creatorType: val.creator_type,
          creatorName: val.creator_name,
          creatorImageUrl: val.creator_image_url,
          viewType: val.view_type,
        };

        if (val.view_type === 'default') {
          return {
            ...defaultReturn,
            isFavorited: val.is_favorited ? true : false,
          };
        } else {
          return { ...defaultReturn };
        }
      }),
      iventCount: queryResult.length,
    };
  }

  async map(mapParams: MapParams): Promise<MapReturn> {
    const {
      sessionId,
      sessionRole,
      startDate,
      endDate,
      latStart,
      latEnd,
      lngStart,
      lngEnd,
      limit,
    } = mapParams;

    const dateClause =
      startDate && endDate
        ? `AND (d.ivent_date::date BETWEEN '${startDate}' AND '${endDate}')`
        : '';
    const geomClause = `ST_Within(l.geom::geometry, ST_MakeEnvelope(${latStart}, ${lngStart}, ${latEnd}, ${lngEnd}, 4326)::geometry)`;

    const query = `
      SELECT
          i.id AS ivent_id,
          ST_X(ST_AsText(geom::geometry)) AS latitude,
          ST_Y(ST_AsText(geom::geometry)) AS longitude
      FROM ivents i
      LEFT JOIN ivent_dates d ON d.ivent_id = i.id
      LEFT JOIN locations l ON l.id = i.location_id
      WHERE ${geomClause}
      ${dateClause}
      LIMIT ${limit};
    `;
    const queryResult = await this.dataSource.query(query);

    return {
      ivents: queryResult.map((val) => ({
        iventId: val.ivent_id,
        coordinates: [val.latitude, val.longitude],
      })),
      iventCount: queryResult.length,
    };
  }

  async searchIvent(searchIventParams: SearchIventParams): Promise<SearchIventReturn> {
    const { sessionId, sessionRole, q, limit, page } = searchIventParams;

    const iventQClause = `i.ivent_name ILIKE '${q}%' OR l.location_name ILIKE '${q}%'`;

    const iventResult = await this.dataSource.query(`
      SELECT
          i.id AS ivent_id,
          i.ivent_name AS ivent_name,
          i.thumbnail_url AS thumbnail_url,
          l.location_name AS location_name,
          i.creator_type AS creator_type,
          COALESCE(p.id, u.id, dis.id) AS creator_id,
          COALESCE(p.page_name, u.username, dis.distributor_name) AS creator_name,
          COALESCE(p.thumbnail_url, u.avatar_url, dis.thumbnail_url) AS creator_image_url,
          'default' AS view_type,
          EXISTS (
              SELECT 1
              FROM user_favorites
              WHERE
                  user_id = '${sessionId}'
                  AND favorited_ivent_id = i.id
          ) AS is_favorited
      FROM ivents i
      LEFT JOIN locations l ON l.id = i.location_id
      LEFT JOIN pages p ON p.id = i.creator_page_id
      LEFT JOIN users u ON u.id = i.creator_user_id
      LEFT JOIN distributors dis ON dis.id = i.creator_distributor_id
      WHERE ${iventQClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      ivents: iventResult.map((val) => ({
        iventId: val.ivent_id,
        iventName: val.ivent_name,
        thumbnailUrl: val.thumbnail_url,
        locationName: val.location_name,
        creatorId: val.creator_id,
        creatorType: val.creator_type,
        creatorName: val.creator_name,
        creatorImageUrl: val.creator_image_url,
        viewType: val.view_type,
        isFavorited: val.is_favorited ? true : false,
      })),
      iventCount: iventResult.length,
    };
  }

  async searchAccount(searchAccountParams: SearchAccountParams): Promise<SearchAccountReturn> {
    const { sessionId, sessionRole, q, limit, page } = searchAccountParams;

    const pageQClause = `p.page_name ILIKE '${q}%'`;
    const userQClause = `u.username ILIKE '${q}%'`;

    const accountResult = await this.dataSource.query(`
      SELECT
          p.id AS account_id,
          p.page_name AS account_name,
          'page' AS account_type,
          p.thumbnail_url AS account_image_url
      FROM pages p
      WHERE ${pageQClause}
      UNION
      SELECT
          u.id AS account_id,
          u.username AS account_name,
          'user' AS account_type,
          u.avatar_url AS account_image_url
      FROM users u
      WHERE ${userQClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      accounts: accountResult.map((val) => ({
        accountId: val.account_id,
        accountName: val.account_name,
        accountType: val.account_type,
        accountImageUrl: val.account_image_url,
      })),
      accountCount: accountResult.length,
    };
  }
}
