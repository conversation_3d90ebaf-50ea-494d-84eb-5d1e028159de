import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { BasicAccountListItem } from 'src/models/basic-account-list-item';
import { IventCardItem } from 'src/models/ivent-card-item';

export class FeedReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(IventCardItem),
    },
  })
  ivents!: IventCardItem[];

  @ApiProperty({ type: 'number' })
  iventCount!: number;
}

export class MapReturn {
  @ApiProperty({
    type: 'array',
    items: {
      type: 'object',
      properties: {
        iventId: { type: 'string' },
        coordinates: { type: 'array', items: { type: 'number' }, minLength: 2 },
      },
    },
  })
  ivents!: {
    iventId: string;
    coordinates: number[];
  }[];

  @ApiProperty({ type: 'number' })
  iventCount!: number;
}

export class SearchAccountReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(BasicAccountListItem) },
  })
  accounts!: BasicAccountListItem[];

  @ApiProperty({ type: 'number' })
  accountCount!: number;
}

export class SearchIventReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(IventCardItem) },
  })
  ivents!: IventCardItem[];

  @ApiProperty({ type: 'number' })
  iventCount!: number;
}
