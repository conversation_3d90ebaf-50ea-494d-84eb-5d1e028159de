import { Injectable, NestMiddleware } from '@nestjs/common';

import { NextFunction, Request, Response } from 'express';

@Injectable()
export class AppLoggerMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction): void {
    const { method, baseUrl, headers, query, body } = req;
    const startTime = Date.now();
    const timestamp = new Date().toISOString();

    if (baseUrl !== '/health') {
      console.log(`\n[${timestamp}] 🔹 REQUEST ${method} ${baseUrl}`);
      console.log(`  Headers: ${JSON.stringify(headers, null, 2)}`);
      console.log(`  Query: ${JSON.stringify(query, null, 2)}`);
      console.log(`  Body: ${JSON.stringify(body, null, 2)}`);

      res.on('finish', () => {
        const { statusCode } = res;
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        const responseTimestamp = new Date().toISOString();

        const statusEmoji = statusCode < 400 ? '✅' : '❌';
        console.log(`[${responseTimestamp}] ${statusEmoji} RESPONSE ${method} ${baseUrl}`);
        console.log(`  Status: ${statusCode}`);
        console.log(`  Duration: ${responseTime}ms`);
      });
    } else {
      console.log(`\n[${timestamp}] 🔹 HEALTH CHECK REQUEST`);
      
      res.on('finish', () => {
        const { statusCode } = res;
        const responseTimestamp = new Date().toISOString();
        console.log(`[${responseTimestamp}] ✅ HEALTH CHECK RESPONSE (${statusCode})`);
      });
    }

    next();
  }
}
