import {
  <PERSON>umn,
  CreateDateColumn,
  Entity as EntityDecorator,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Ivent } from './ivent.entity';

@EntityDecorator('distributors')
export class Distributor {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'varchar' })
  distributor_name!: string;

  @Column({ type: 'varchar' })
  thumbnail_url!: string;

  @Column({ type: 'varchar' })
  website_url!: string;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from entities
  @OneToMany(() => Ivent, (ivent) => ivent.creator_distributor)
  created_ivents?: Ivent[];
}
