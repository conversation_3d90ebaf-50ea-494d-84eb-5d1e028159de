import { ApiProperty } from '@nestjs/swagger';
import { AccountTypeEnum } from 'src/entities';

export class BasicAccountListItem {
  @ApiProperty({ type: 'string' })
  accountId!: string;

  @ApiProperty({ type: 'string' })
  accountName!: string;

  @ApiProperty({ type: 'string', enum: Object.values(AccountTypeEnum) })
  accountType!: AccountTypeEnum;

  @ApiProperty({ type: 'string', nullable: true })
  accountImageUrl!: string | null;
}
