export function hydrateComputedList<T>(
  entities: T[],
  raw: any[],
  fields: (keyof T)[] | { field: keyof T; transformer?: { from: (value: any) => any } }[],
): T[] {
  return entities.map((entity, index) => {
    const rawRow = raw[index];

    // Handle both old format (array of field names) and new format (array of field configs)
    const fieldConfigs =
      Array.isArray(fields) && fields.length > 0 && typeof fields[0] === 'string'
        ? (fields as (keyof T)[]).map((field) => ({ field, transformer: undefined }))
        : (fields as { field: keyof T; transformer?: { from: (value: any) => any } }[]);

    for (const config of fieldConfigs) {
      const field = config.field;
      if (rawRow[field as string] !== undefined) {
        try {
          let value = rawRow[field as string];
          if (config.transformer?.from) {
            value = config.transformer.from(value);
          }
          entity[field] = value;
        } catch {}
      }
    }
    return entity;
  });
}
