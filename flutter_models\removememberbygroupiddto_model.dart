// Generated Dart model for RemoveMemberByGroupIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'removememberbygroupiddto_model.freezed.dart';
part 'removememberbygroupiddto_model.g.dart';

@freezed
class RemoveMemberByGroupIdDtoModel with _$RemoveMemberByGroupIdDtoModel {
  const factory RemoveMemberByGroupIdDtoModel({
    required String userId,
  }) = _RemoveMemberByGroupIdDtoModel;

  factory RemoveMemberByGroupIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$RemoveMemberByGroupIdDtoModelFromJson(json);
}
