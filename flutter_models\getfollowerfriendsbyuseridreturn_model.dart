// Generated Dart model for GetFollowerFriendsByUserIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getfollowerfriendsbyuseridreturn_model.freezed.dart';
part 'getfollowerfriendsbyuseridreturn_model.g.dart';

@freezed
class GetFollowerFriendsByUserIdReturnModel with _$GetFollowerFriendsByUserIdReturnModel {
  const factory GetFollowerFriendsByUserIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int friendCount,
  }) = _GetFollowerFriendsByUserIdReturnModel;

  factory GetFollowerFriendsByUserIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetFollowerFriendsByUserIdReturnModelFromJson(json);
}
