import { ApiProperty } from '@nestjs/swagger';
import { Matches } from 'class-validator';

export class SendVerificationCodeDto {
  @ApiProperty({ type: 'string', example: '+90(500)4003020' })
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/)
  phoneNumber!: string;
}

export class ValidateDto {
  @ApiProperty({ type: 'string', example: '123456' })
  @Matches(/^\d{6}$/)
  validationCode!: string;

  @ApiProperty({ type: 'string', example: '+90(500)4003020' })
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/)
  phoneNumber!: string;
}
