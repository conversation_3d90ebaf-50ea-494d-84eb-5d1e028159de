// Generated Dart model for RemoveCollabByIventIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'removecollabbyiventiddto_model.freezed.dart';
part 'removecollabbyiventiddto_model.g.dart';

@freezed
class RemoveCollabByIventIdDtoModel with _$RemoveCollabByIventIdDtoModel {
  const factory RemoveCollabByIventIdDtoModel({
    required String collabId,
    required String collabType,
  }) = _RemoveCollabByIventIdDtoModel;

  factory RemoveCollabByIventIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$RemoveCollabByIventIdDtoModelFromJson(json);
}
