// Generated Dart model for CreatePageDto
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'createpagedto_model.freezed.dart';
part 'createpagedto_model.g.dart';

@freezed
class CreatePageDtoModel with _$CreatePageDtoModel {
  const factory CreatePageDtoModel({
    required String pageName,
    String? thumbnailUrl,
    String? websiteUrl,
    String? description,
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isEdu,
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool haveMembership,
    required List<String> tags,
    required List<String> creatorIds,
    required String locationId,
    required String userId,
    required String newDescription,
    required String newLink,
    required String newLocationId,
  }) = _CreatePageDtoModel;

  factory CreatePageDtoModel.fromJson(Map<String, dynamic> json) =>
      _$CreatePageDtoModelFromJson(json);
}
