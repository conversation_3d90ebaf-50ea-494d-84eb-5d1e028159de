import { stringToArrayTransformer, stringToNumberTransformer } from 'src/utils/value-transformers';
import { JoinColumn, ManyToOne, ViewColumn, ViewEntity } from 'typeorm';
import { Ivent } from '../ivent.entity';
import { Page } from '../page.entity';
import { User } from '../user.entity';
import { IventUsers } from './ivent-users.view.entity';

@ViewEntity({
  name: 'collab_summary_of_ivent',
  expression: `
    SELECT
        iu.account_id AS account_id,
        iu.ivent_id AS ivent_id,
        count(iu.account_id) AS collab_count,
        array_to_string(
            (
                array_agg(
                    COALESCE(p.page_name, u.username)
                )
            ) [:3],
            ','
        ) AS collab_names,
        array_to_string(
            (
                array_agg(
                    COALESCE(p.thumbnail_url, u.avatar_url)
                )
            ) [:6],
            ','
        ) AS collab_thumbnail_urls
    FROM
        ivent_users iu
        LEFT JOIN pages p ON p.id = iu.account_id
        LEFT JOIN users u ON u.id = iu.account_id
    WHERE
        iu.type IN ('page', 'user')
        AND iu.status IN ('accepted', 'admin')
    GROUP BY
        iu.ivent_id,
        iu.account_id
  `,
  dependsOn: [() => IventUsers],
})
export class CollabSummaryOfIvent {
  @ViewColumn()
  account_id!: string;

  @ManyToOne(() => Page, { nullable: true })
  @JoinColumn({ name: 'account_id' })
  page?: Page | null;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'account_id' })
  user?: User | null;

  @ViewColumn()
  ivent_id!: string;

  @ManyToOne(() => Ivent)
  @JoinColumn({ name: 'ivent_id' })
  ivent?: Ivent;

  @ViewColumn({ transformer: stringToNumberTransformer })
  collab_count!: number;

  @ViewColumn({ transformer: stringToArrayTransformer })
  collab_names!: string[];

  @ViewColumn({ transformer: stringToArrayTransformer })
  collab_thumbnail_urls!: (string | null)[];
}
