export type CreateLocationParams = {
  sessionId: string;
  sessionRole: string;
  locationName: string;
  openAddress: string;
  coordinates: number;
  state: string;
};

export type GetLatestLocationsParams = {
  sessionId: string;
  sessionRole: string;
  limit: number;
  page: number;
};

export type GetLocationsParams = {
  sessionId: string;
  sessionRole: string;
  q: string;
  limit: number;
  page: number;
};

export type GetPlacesParams = {
  sessionId: string;
  sessionRole: string;
  lat: number;
  lng: number;
  radius: number;
};
