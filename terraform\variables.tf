variable "aws_region" {
  type        = string
  description = "aws_region"
  default     = "eu-central-1"
}

variable "aws_profile" {
  type        = string
  description = "aws_profile"
}

variable "aws_project_name" {
  type        = string
  description = "aws_project_name"
}


variable "aws_prefix" {
  type        = string
  description = "aws_prefix"
}


variable "aws_ssl_domain" {
  type        = string
  description = "aws_ssl_domain"
}

variable "aws_root_domain" {
  type        = string
  description = "aws_root_domain"
}


variable "aws_ecr_repo_name" {
  type        = string
  description = "aws_ecr_repo_name"
}


variable "aws_rds_parameter_group" {
  type        = string
  description = "aws_rds_parameter_group"
}


variable "aws_ecs_cluster_name" {
  type        = string
  description = "aws_ecs_cluster_name"
}

variable "aws_rds_identifier_name" {
  type        = string
  description = "aws_rds_identifier_name"
}

variable "aws_ecs_desired_count" {
  type        = number
  description = "aws_ecs_desired_count"
}

variable "app_env" {
  type        = string
  description = "app_env"
}

variable "app_domain" {
  type        = string
  description = "app_domain"
}

variable "app_port" {
  type        = string
  description = "app_port"
}


variable "database_name" {
  type        = string
  description = "database_name"
}


variable "database_user" {
  type        = string
  description = "database_user"
}


variable "database_password" {
  type        = string
  description = "database_password"
}

variable "database_port" {
  type        = string
  description = "database_port"
}
variable "jwt_secret" {
  type        = string
  description = "jwt_secret"
}

variable "s3_access_key" {
  description = "s3_access_key"
  type        = string
}

variable "s3_secret_key" {
  description = "s3_scret_key"
  type        = string
}

variable "s3_region" {
  description = "S3_REGION"
  type        = string
}
