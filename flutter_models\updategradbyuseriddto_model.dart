// Generated Dart model for UpdateGradByUserIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'updategradbyuseriddto_model.freezed.dart';
part 'updategradbyuseriddto_model.g.dart';

@freezed
class UpdateGradByUserIdDtoModel with _$UpdateGradByUserIdDtoModel {
  const factory UpdateGradByUserIdDtoModel({
    required String newGrad,
  }) = _UpdateGradByUserIdDtoModel;

  factory UpdateGradByUserIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$UpdateGradByUserIdDtoModelFromJson(json);
}
