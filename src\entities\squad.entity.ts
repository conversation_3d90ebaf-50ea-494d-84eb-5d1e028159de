import {
  <PERSON><PERSON><PERSON>,
  CreateDate<PERSON><PERSON>umn,
  Entity,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Ivent } from './ivent.entity';
import { MemoryFolder } from './memory-folder.entity';
import { SquadMembership } from './squad-membership.entity';
import { User } from './user.entity';
import { VibeFolder } from './vibe-folder.entity';
import { SquadFriendships } from './views';

@Entity('squads')
export class Squad {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'uuid' })
  creator_id!: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'creator_id' })
  creator?: User;

  @Column({ type: 'uuid' })
  ivent_id!: string;

  @ManyToOne(() => Ivent, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'ivent_id' })
  ivent?: Ivent;

  @Column({ type: 'uuid' })
  vibe_folder_id!: string;

  @OneToOne(() => VibeFolder, { onDelete: 'RESTRICT' })
  @JoinColumn({ name: 'vibe_folder_id' })
  vibe_folder?: VibeFolder;

  @Column({ type: 'uuid' })
  memory_folder_id!: string;

  @OneToOne(() => MemoryFolder, { onDelete: 'RESTRICT' })
  @JoinColumn({ name: 'memory_folder_id' })
  memory_folder?: MemoryFolder;

  @CreateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  created_at!: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone', default: () => 'now()' })
  updated_at!: Date;

  // Additional relationships from entities
  @OneToMany(() => SquadMembership, (squadMembership) => squadMembership.squad)
  memberships?: SquadMembership[];

  // Additional relationships from views
  @OneToMany(() => SquadFriendships, (squadFriendships) => squadFriendships.squad)
  squad_friendships?: SquadFriendships[];
}
