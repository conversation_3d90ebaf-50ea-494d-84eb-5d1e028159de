import { Body, Controller, DefaultValuePipe, Get, ParseIntPipe, Post, Query, Res } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { ApiResponseObject } from 'src/models/api-response-object';
import { LocationsService } from './locations.service';
import { CreateLocationDto } from './models/locations.dto';
import {
  CreateLocationReturn,
  GetLatestLocationsReturn,
  GetLocationsReturn,
  GetPlacesReturn,
} from './models/locations.returns';

@ApiTags('locations')
@Controller('locations')
export class LocationsController {
  constructor(private readonly locationsService: LocationsService) {}

  @ApiOperation({
    summary:
      'Haritadan seçilen yer bilgileri ile yeni bir konum oluşturur (konum GoogleMapsAPI ya da pin ile seçilebilir)',
  })
  @ApiResponseObject({
    model: CreateLocationReturn,
  })
  @Post('create')
  async createLocation(@Body() createLocationDto: CreateLocationDto, @Res({ passthrough: true }) res: Response) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.locationsService.createLocation({
      sessionId,
      sessionRole,
      ...createLocationDto,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Haritadan gözüken bölgedeki konumları listeler (hem GoogleMapsAPI hem de kendi konumlarımız olabilir)',
  })
  @ApiResponseObject({
    model: GetLocationsReturn,
  })
  @ApiQuery({ name: 'q', required: false, type: 'string' })
  @ApiQuery({ name: 'limit', required: false, type: 'number' })
  @ApiQuery({ name: 'page', required: false, type: 'number' })
  @Get('search')
  async searchLocations(
    @Res({ passthrough: true }) res: Response,
    @Query('q', new DefaultValuePipe('')) q: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.locationsService.getLocations({
      sessionId,
      sessionRole,
      q,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Son seçilen konumları listeler',
  })
  @ApiResponseObject({
    model: GetLatestLocationsReturn,
  })
  @ApiQuery({ name: 'limit', required: false, type: 'number' })
  @ApiQuery({ name: 'page', required: false, type: 'number' })
  @Get('latest')
  async getLatestLocations(
    @Res({ passthrough: true }) res: Response,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.locationsService.getLatestLocations({
      sessionId,
      sessionRole,
      limit,
      page,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }

  @ApiOperation({
    summary: 'GoogleMapsAPI ile seçilen konumları listeler',
  })
  @ApiResponseObject({
    model: GetPlacesReturn,
  })
  @Get('places')
  async getPlaces(
    @Res({ passthrough: true }) res: Response,
    @Query('lat') lat: number,
    @Query('lng') lng: number,
    @Query('radius') radius: number,
  ) {
    const sessionId = res.locals.decoded ? res.locals.decoded._id : null;
    const sessionRole = res.locals.decoded ? res.locals.decoded.role : null;

    const result = await this.locationsService.getPlaces({
      sessionId,
      sessionRole,
      lat,
      lng,
      radius,
    });
    return {
      status: 200,
      message: 'Success',
      data: result,
    };
  }
}
