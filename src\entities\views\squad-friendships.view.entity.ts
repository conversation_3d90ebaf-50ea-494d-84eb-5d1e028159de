import { Join<PERSON><PERSON>umn, ManyToOne, ViewColumn, ViewEntity } from 'typeorm';
import { Squad } from '../squad.entity';
import { User } from '../user.entity';

@ViewEntity({
  name: 'squad_friendships',
  expression: `
    SELECT
        sm_main.member_id,
        sm_main.squad_id,
        sm_other.member_id AS other_member_id,
        COALESCE(
            sm_other.updated_at,
            sm_other.created_at
        ) AS added_at
    FROM
        squad_memberships sm_main
        LEFT JOIN squad_memberships sm_other ON sm_other.squad_id = sm_main.squad_id
    WHERE
        sm_main.status IN ('joined', 'accepted')
        AND sm_main.member_id <> sm_other.member_id
    ORDER BY sm_main.member_id, sm_main.squad_id
  `,
})
export class SquadFriendships {
  @ViewColumn()
  member_id!: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'member_id' })
  member?: User;

  @ViewColumn()
  squad_id!: string;

  @ManyToOne(() => Squad)
  @JoinColumn({ name: 'squad_id' })
  squad?: Squad;

  @ViewColumn()
  other_member_id!: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'other_member_id' })
  other_member?: User;

  @ViewColumn()
  added_at!: Date;
}
