import { ApiProperty } from '@nestjs/swagger';

export class MemoryFolderCardItem {
  @ApiProperty({ type: 'string' })
  memoryFolderId!: string;

  @ApiProperty({ type: 'string', nullable: true })
  thumbnailUrl!: string | null;

  @ApiProperty({ type: 'string' })
  iventId!: string;

  @ApiProperty({ type: 'string' })
  iventName!: string;

  @ApiProperty({ type: 'string' })
  date!: string;

  @ApiProperty({ type: 'number' })
  memberCount!: number;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
  })
  memberNames!: string[];

  @ApiProperty({ type: 'string', nullable: true })
  createdAt!: string | null;
}
