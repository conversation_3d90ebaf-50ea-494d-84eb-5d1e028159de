// Generated Dart model for GetVibeFoldersByPageIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getvibefoldersbypageidreturn_model.freezed.dart';
part 'getvibefoldersbypageidreturn_model.g.dart';

@freezed
class GetVibeFoldersByPageIdReturnModel with _$GetVibeFoldersByPageIdReturnModel {
  const factory GetVibeFoldersByPageIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int vibeFolderCount,
  }) = _GetVibeFoldersByPageIdReturnModel;

  factory GetVibeFoldersByPageIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetVibeFoldersByPageIdReturnModelFromJson(json);
}
