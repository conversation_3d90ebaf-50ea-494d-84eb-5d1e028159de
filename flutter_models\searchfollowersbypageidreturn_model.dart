// Generated Dart model for SearchFollowersByPageIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchfollowersbypageidreturn_model.freezed.dart';
part 'searchfollowersbypageidreturn_model.g.dart';

@freezed
class SearchFollowersByPageIdReturnModel with _$SearchFollowersByPageIdReturnModel {
  const factory SearchFollowersByPageIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int userCount,
  }) = _SearchFollowersByPageIdReturnModel;

  factory SearchFollowersByPageIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchFollowersByPageIdReturnModelFromJson(json);
}
