// Generated Dart model for GetLatestIventsReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getlatestiventsreturn_model.freezed.dart';
part 'getlatestiventsreturn_model.g.dart';

@freezed
class GetLatestIventsReturnModel with _$GetLatestIventsReturnModel {
  const factory GetLatestIventsReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,
  }) = _GetLatestIventsReturnModel;

  factory GetLatestIventsReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetLatestIventsReturnModelFromJson(json);
}
