import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { EmptyReturn } from 'src/models/empty-return';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { DataSource } from 'typeorm';
import {
  AddModeratorByGroupIdParams,
  InviteMembersByGroupIdParams,
  LeaveGroupByGroupIdParams,
  RemoveMemberByGroupIdParams,
  RemoveModeratorByGroupIdParams,
  SearchGroupMembersByGroupIdParams,
  SearchInvitableUsersByGroupIdParams,
  SearchUsersForGroupCreationParams,
  TransferAdministrationByGroupIdParams,
} from './models/group-memberships.params';
import {
  SearchGroupMembersByGroupIdReturn,
  SearchInvitableUsersByGroupIdReturn,
  SearchUsersForGroupCreationReturn,
} from './models/group-memberships.returns';

@Injectable()
export class GroupMembershipsService {
  constructor(private dataSource: DataSource) {}

  async addModeratorByGroupId(addModeratorByGroupIdParams: AddModeratorByGroupIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, groupId, userId } = addModeratorByGroupIdParams;

    if (sessionId === userId) {
      throw new HttpException("You can't add yourself as the moderator", HttpStatus.BAD_REQUEST);
    }

    const membershipResult = await this.dataSource.query(`
      SELECT
          id AS group_membership_id, 
          status AS status,
          member_id AS member_id
      FROM group_memberships
      WHERE group_id = '${groupId}'
      AND status IN ('admin', 'moderator', 'accepted')
      AND (member_id = '${sessionId}' OR member_id = '${userId}');
    `);

    const userObject = membershipResult.find((val) => val.member_id === userId);
    const userStatus = userObject ? userObject.status : null;
    const sessionUserObject = membershipResult.find((val) => val.member_id === sessionId);
    const sessionUserStatus = sessionUserObject ? sessionUserObject.status : null;

    if (userObject === null) {
      throw new HttpException('There is no such user in the group', HttpStatus.BAD_REQUEST);
    }
    if (sessionUserObject === null) {
      throw new HttpException('You are not in the group', HttpStatus.BAD_REQUEST);
    }

    if (userStatus !== 'accepted') {
      throw new HttpException('User is already authorized', HttpStatus.BAD_REQUEST);
    }
    if (sessionUserStatus !== 'admin') {
      throw new HttpException('You are not authorized', HttpStatus.BAD_REQUEST);
    }

    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'group_memberships',
        values: {
          member_id: userId,
          group_id: groupId,
          status: 'moderator',
        },
      }),
    );

    return {};
  }

  async removeModeratorByGroupId(removeModeratorByGroupIdParams: RemoveModeratorByGroupIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId, groupId } = removeModeratorByGroupIdParams;

    if (sessionId === userId) {
      throw new HttpException("You can't remove yourself as the moderator", HttpStatus.BAD_REQUEST);
    }

    const membershipResult = await this.dataSource.query(`
      SELECT
          id AS group_membership_id, 
          status AS status,
          member_id AS member_id
      FROM group_memberships
      WHERE group_id = '${groupId}'
      AND status IN ('admin', 'moderator', 'accepted')
      AND (member_id = '${sessionId}' OR member_id = '${userId}');
    `);

    const userObject = membershipResult.find((val) => val.member_id === userId);
    const userStatus = userObject ? userObject.status : null;
    const userMembershipId = userObject ? userObject.group_membership_id : null;
    const sessionUserObject = membershipResult.find((val) => val.member_id === sessionId);
    const sessionUserStatus = sessionUserObject ? sessionUserObject.status : null;

    if (userObject === null) {
      throw new HttpException('There is no such user in the group', HttpStatus.BAD_REQUEST);
    }
    if (sessionUserObject === null) {
      throw new HttpException('You are not in the group', HttpStatus.BAD_REQUEST);
    }

    if (userStatus === 'accepted') {
      throw new HttpException('User is already not authorized', HttpStatus.BAD_REQUEST);
    }
    if (sessionUserStatus !== 'admin') {
      throw new HttpException('You are not authorized', HttpStatus.BAD_REQUEST);
    }

    await this.dataSource
      .createQueryBuilder()
      .update('group_memberships')
      .set({
        status: 'accepted',
      })
      .where({
        id: userMembershipId,
      })
      .execute();

    return {};
  }

  async transferAdministrationByGroupId(
    transferAdministrationByGroupIdParams: TransferAdministrationByGroupIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId, groupId } = transferAdministrationByGroupIdParams;

    if (sessionId === userId) {
      throw new HttpException("You can't transfer administration to yourself", HttpStatus.BAD_REQUEST);
    }

    const membershipResult = await this.dataSource.query(`
      SELECT
          id AS group_membership_id, 
          status AS status,
          member_id AS member_id
      FROM group_memberships
      WHERE group_id = '${groupId}'
      AND status IN ('admin', 'moderator', 'accepted')
      AND (member_id = '${sessionId}' OR member_id = '${userId}');
    `);

    const userObject = membershipResult.find((val) => val.member_id === userId);
    const userStatus = userObject ? userObject.status : null;
    const userMembershipId = userObject ? userObject.group_membership_id : null;
    const sessionUserObject = membershipResult.find((val) => val.member_id === sessionId);
    const sessionUserStatus = sessionUserObject ? sessionUserObject.status : null;
    const sessionUserMembershipId = sessionUserObject ? sessionUserObject.group_membership_id : null;

    if (userObject === null) {
      throw new HttpException('There is no such user in the group', HttpStatus.BAD_REQUEST);
    }
    if (sessionUserObject === null) {
      throw new HttpException('You are not in the group', HttpStatus.BAD_REQUEST);
    }

    if (userStatus !== 'accepted') {
      throw new HttpException('User is already authorized', HttpStatus.BAD_REQUEST);
    }
    if (sessionUserStatus !== 'admin') {
      throw new HttpException('You are not authorized', HttpStatus.BAD_REQUEST);
    }

    await this.dataSource
      .createQueryBuilder()
      .update('group_memberships')
      .set({
        status: 'admin',
      })
      .where({
        id: userMembershipId,
      })
      .execute();

    await this.dataSource
      .createQueryBuilder()
      .update('group_memberships')
      .set({
        status: 'accepted',
      })
      .where({
        id: sessionUserMembershipId,
      })
      .execute();

    return {};
  }

  async searchInvitableUsersByGroupId(
    searchInvitableUsersByGroupIdParams: SearchInvitableUsersByGroupIdParams,
  ): Promise<SearchInvitableUsersByGroupIdReturn> {
    const { sessionId, sessionRole, groupId, q, limit, page } = searchInvitableUsersByGroupIdParams;

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const membershipResult = await this.dataSource.query(`
      SELECT id AS group_membership_id
      FROM group_memberships
      WHERE group_id = '${groupId}'
      AND status IN ('admin', 'moderator')
      AND member_id = '${sessionId}';
    `);
    if (membershipResult.length === 0) {
      throw new HttpException('You are not authorized', HttpStatus.BAD_REQUEST);
    }

    const result = await this.dataSource.query(`
      WITH ActiveGroupFriendships AS (
          -- Get all active group memberships of the session user
          SELECT member_id
          FROM group_memberships
          WHERE group_id = '${groupId}'
          AND status IN ('admin', 'moderator', 'accepted', 'invited')
          AND member_id != '${sessionId}'
      )
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM user_friendships ufr
      LEFT JOIN users u ON u.id = ufr.friend_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      WHERE ufr.user_id = '${sessionId}'
      AND ufr.friend_id NOT IN (SELECT member_id FROM ActiveGroupFriendships)
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      users: result.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_Url,
        university: val.university,
      })),
      userCount: result.length,
    };
  }

  async searchUsersForGroupCreation(
    searchUsersForGroupCreationParams: SearchUsersForGroupCreationParams,
  ): Promise<SearchUsersForGroupCreationReturn> {
    const { sessionId, sessionRole, q, limit, page } = searchUsersForGroupCreationParams;

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const result = await this.dataSource.query(`
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM user_friendships ufr
      LEFT JOIN users u ON u.id = ufr.friend_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      WHERE ufr.user_id = '${sessionId}'
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      users: result.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_Url,
        university: val.university,
      })),
      userCount: result.length,
    };
  }

  async searchGroupMembersByGroupId(
    searchGroupMembersByGroupIdParams: SearchGroupMembersByGroupIdParams,
  ): Promise<SearchGroupMembersByGroupIdReturn> {
    const { sessionId, sessionRole, groupId, q, limit, page } = searchGroupMembersByGroupIdParams;

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const membershipResult = await this.dataSource.query(`
      SELECT id AS group_membership_id
      FROM group_memberships
      WHERE group_id = '${groupId}'
      AND status IN ('admin', 'moderator', 'accepted')
      AND member_id = '${sessionId}';
    `);
    if (membershipResult.length === 0) {
      throw new HttpException('You are not in the group', HttpStatus.BAD_REQUEST);
    }

    const result = await this.dataSource.query(`
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM group_memberships gm
      LEFT JOIN users u ON u.id = gm.member_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      WHERE gm.group_id = '${groupId}'
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      members: result.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_Url,
        university: val.university,
        role: val.role,
        isFriend: val.isFriend,
      })),
      memberCount: result.length,
    };
  }

  async leaveGroupByGroupId(leaveGroupByGroupIdParams: LeaveGroupByGroupIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, groupId } = leaveGroupByGroupIdParams;

    const membershipResult = await this.dataSource.query(`
      SELECT id AS group_membership_id
      FROM group_memberships
      WHERE group_id = '${groupId}'
      AND status IN ('admin', 'moderator', 'accepted')
      AND member_id = '${sessionId}';
    `);
    if (membershipResult.length === 0) {
      throw new HttpException('You are not in the group', HttpStatus.BAD_REQUEST);
    }

    await this.dataSource
      .createQueryBuilder()
      .update('group_memberships')
      .set({
        status: 'left',
      })
      .where({
        id: membershipResult[0].group_membership_id,
      })
      .execute();

    return {};
  }

  async inviteMembersByGroupId(inviteMembersByGroupIdParams: InviteMembersByGroupIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, groupId, userIds } = inviteMembersByGroupIdParams;

    const membershipResult = await this.dataSource.query(`
      SELECT id AS group_membership_id
      FROM group_memberships
      WHERE group_id = '${groupId}'
      AND status IN ('admin', 'moderator')
      AND member_id = '${sessionId}';
    `);
    if (membershipResult.length === 0) {
      throw new HttpException('You are not authorized', HttpStatus.BAD_REQUEST);
    }

    // Check for the users that is a member in the group OR invited to the group, and later filter them
    const groupMembersResult = (
      await this.dataSource.query(`
      SELECT member_id
      FROM group_memberships
      WHERE group_id = '${groupId}'
      AND status IN ('admin', 'moderator', 'accepted', 'invited')
      AND member_id != '${sessionId}';
    `)
    ).map((val) => val.account_id);
    const availableUsers = Array.from(new Set(userIds)).filter((item) => !groupMembersResult.includes(item));

    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'group_memberships',
        values: availableUsers.map((val) => ({
          member_id: val,
          group_id: groupId,
          inviter_id: sessionId,
          status: 'pending',
        })),
      }),
    );

    return {};
  }

  async removeMemberByGroupId(removeMemberByGroupIdParams: RemoveMemberByGroupIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, groupId, userId } = removeMemberByGroupIdParams;

    const membershipResult = await this.dataSource.query(`
      SELECT
          id AS group_membership_id, 
          status AS status,
          member_id AS member_id
      FROM group_memberships
      WHERE group_id = '${groupId}'
      AND status IN ('admin', 'moderator', 'accepted')
      AND (member_id = '${sessionId}' OR member_id = '${userId}');
    `);

    const userObject = membershipResult.find((val) => val.member_id === userId);
    const userStatus = userObject ? userObject.status : null;
    const userMembershipId = userObject ? userObject.group_membership_id : null;
    const sessionUserObject = membershipResult.find((val) => val.member_id === sessionId);
    const sessionUserStatus = sessionUserObject ? sessionUserObject.status : null;

    if (userObject === null) {
      throw new HttpException('There is no such user in the group', HttpStatus.BAD_REQUEST);
    }
    if (sessionUserObject === null) {
      throw new HttpException('You are not in the group', HttpStatus.BAD_REQUEST);
    }

    if (userStatus === 'admin') {
      throw new HttpException('You are not authorized', HttpStatus.BAD_REQUEST);
    }
    if (sessionUserStatus !== 'admin') {
      throw new HttpException('You are not authorized', HttpStatus.BAD_REQUEST);
    }

    await this.dataSource
      .createQueryBuilder()
      .update('group_memberships')
      .set({
        status: 'removed',
      })
      .where({
        id: userMembershipId[0].group_membership_id,
      })
      .execute();

    return {};
  }
}
