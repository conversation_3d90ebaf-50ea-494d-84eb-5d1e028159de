import { ApiProperty } from '@nestjs/swagger';
import { AuthEnum } from 'src/constants/enums/auth-enum';

export class ValidateReturn {
  @ApiProperty({ type: 'string', nullable: true })
  token!: string | null;

  @ApiProperty({ type: 'string', nullable: true })
  userId!: string | null;

  @ApiProperty({ type: 'string', nullable: true })
  role!: string | null;

  @ApiProperty({ type: 'string', nullable: true })
  username!: string | null;

  @ApiProperty({ type: 'string', nullable: true })
  fullname!: string | null;

  @ApiProperty({ type: 'string', nullable: true })
  avatarUrl!: string | null;

  @ApiProperty({ type: 'string', enum: Object.values(AuthEnum) })
  type!: AuthEnum;
}
