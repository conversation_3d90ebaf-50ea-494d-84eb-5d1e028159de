// Generated Dart model for SearchPageAdminsByPageIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchpageadminsbypageidreturn_model.freezed.dart';
part 'searchpageadminsbypageidreturn_model.g.dart';

@freezed
class SearchPageAdminsByPageIdReturnModel with _$SearchPageAdminsByPageIdReturnModel {
  const factory SearchPageAdminsByPageIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int userCount,
  }) = _SearchPageAdminsByPageIdReturnModel;

  factory SearchPageAdminsByPageIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchPageAdminsByPageIdReturnModelFromJson(json);
}
