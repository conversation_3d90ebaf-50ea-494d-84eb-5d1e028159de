import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { SearchUniversitiesParams } from './models/universities.params';
import { SearchUniversitiesReturn } from './models/universities.returns';

@Injectable()
export class UniversitiesService {
  constructor(private dataSource: DataSource) {}

  async searchUniversities(searchUniversitiesParams: SearchUniversitiesParams): Promise<SearchUniversitiesReturn> {
    //
    const { sessionId, sessionRole, q, limit, page } = searchUniversitiesParams;

    const qClause = q ? `(uni.university_code ILIKE '${q}%' OR uni.university_name ILIKE '${q}%')` : 'TRUE';

    const universitiesResult = await this.dataSource.query(`
      SELECT
        uni.university_name AS university_name,
        uni.image_url AS university_image_url,
        l.state AS university_location_state
      FROM universities uni
      LEFT JOIN locations l ON l.id = uni.location_id
      WHERE ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      universities: universitiesResult.map((val) => ({
        universityName: val.university_name,
        universityImageUrl: val.university_image_url,
        universityLocationState: val.university_location_state,
      })),
      universityCount: universitiesResult.length,
    };
  }
}
