// Generated Dart model for UpdateDateByIventIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'updatedatebyiventiddto_model.freezed.dart';
part 'updatedatebyiventiddto_model.g.dart';

@freezed
class UpdateDateByIventIdDtoModel with _$UpdateDateByIventIdDtoModel {
  const factory UpdateDateByIventIdDtoModel({
    required List<String> newDates,
  }) = _UpdateDateByIventIdDtoModel;

  factory UpdateDateByIventIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$UpdateDateByIventIdDtoModelFromJson(json);
}
