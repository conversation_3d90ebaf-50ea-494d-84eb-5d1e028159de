// Generated Dart model for SearchAccountReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchaccountreturn_model.freezed.dart';
part 'searchaccountreturn_model.g.dart';

@freezed
class SearchAccountReturnModel with _$SearchAccountReturnModel {
  const factory SearchAccountReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int accountCount,
  }) = _SearchAccountReturnModel;

  factory SearchAccountReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchAccountReturnModelFromJson(json);
}
