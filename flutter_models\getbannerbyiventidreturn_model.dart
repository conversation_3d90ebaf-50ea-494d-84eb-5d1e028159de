// Generated Dart model for GetBannerByIventIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getbannerbyiventidreturn_model.freezed.dart';
part 'getbannerbyiventidreturn_model.g.dart';

@freezed
class GetBannerByIventIdReturnModel with _$GetBannerByIventIdReturnModel {
  const factory GetBannerByIventIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,
  }) = _GetBannerByIventIdReturnModel;

  factory GetBannerByIventIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetBannerByIventIdReturnModelFromJson(json);
}
