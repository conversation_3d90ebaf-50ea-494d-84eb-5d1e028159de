// Generated Dart model for UpdateLocationByIventIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'updatelocationbyiventiddto_model.freezed.dart';
part 'updatelocationbyiventiddto_model.g.dart';

@freezed
class UpdateLocationByIventIdDtoModel with _$UpdateLocationByIventIdDtoModel {
  const factory UpdateLocationByIventIdDtoModel({
    required String newlocationId,
  }) = _UpdateLocationByIventIdDtoModel;

  factory UpdateLocationByIventIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$UpdateLocationByIventIdDtoModelFromJson(json);
}
