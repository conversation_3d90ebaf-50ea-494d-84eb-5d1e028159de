import { ApiProperty } from '@nestjs/swagger';

export class VibeFolderCardItem {
  @ApiProperty({ type: 'string' })
  vibeFolderId!: string;

  @ApiProperty({ type: 'string', nullable: true })
  thumbnailUrl!: string | null;

  @ApiProperty({ type: 'string' })
  iventId!: string;

  @ApiProperty({ type: 'string' })
  iventName!: string;

  @ApiProperty({ type: 'string' })
  vibeId!: string;

  @ApiProperty({ type: 'number' })
  memberCount!: number;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
  })
  memberNames!: string[];
}
