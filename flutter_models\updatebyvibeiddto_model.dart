// Generated Dart model for UpdateByVibeIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'updatebyvibeiddto_model.freezed.dart';
part 'updatebyvibeiddto_model.g.dart';

@freezed
class UpdateByVibeIdDtoModel with _$UpdateByVibeIdDtoModel {
  const factory UpdateByVibeIdDtoModel({
    required String newCaption,
  }) = _UpdateByVibeIdDtoModel;

  factory UpdateByVibeIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$UpdateByVibeIdDtoModelFromJson(json);
}
