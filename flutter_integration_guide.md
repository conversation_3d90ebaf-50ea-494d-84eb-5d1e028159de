# Flutter API Integration Guide for Ivent API

## 1. Recommended Architecture

### Repository Pattern with Service Layer

```
lib/
├── data/
│   ├── models/           # Data models (DTOs)
│   ├── repositories/     # Repository implementations
│   └── services/         # API service classes
├── domain/
│   ├── entities/         # Business entities
│   ├── repositories/     # Repository interfaces
│   └── usecases/         # Business logic
└── presentation/
    ├── providers/        # State management (Riverpod/Bloc)
    └── screens/          # UI components
```

## 2. Dependencies Setup

Add these to your `pubspec.yaml`:

```yaml
dependencies:
  # HTTP Client
  dio: ^5.4.0

  # JSON Serialization
  json_annotation: ^4.8.1
  freezed_annotation: ^2.4.1

  # State Management
  flutter_riverpod: ^2.4.9

  # Storage
  flutter_secure_storage: ^9.0.0
  shared_preferences: ^2.2.2

  # Utilities
  logger: ^2.0.2+1
  pretty_dio_logger: ^1.3.1

dev_dependencies:
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  freezed: ^2.4.6
```

## 3. HTTP Client Configuration

### Base API Client with Dio

```dart
// lib/data/services/api_client.dart
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

class ApiClient {
  static const String baseUrl = 'https://your-api-domain.com';
  static const String apiVersion = '';

  late final Dio _dio;
  final FlutterSecureStorage _storage = const FlutterSecureStorage();

  ApiClient() {
    _dio = Dio(BaseOptions(
      baseUrl: '$baseUrl$apiVersion',
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    _setupInterceptors();
  }

  void _setupInterceptors() {
    // Auth Interceptor
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final token = await _storage.read(key: 'auth_token');
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        if (error.response?.statusCode == 401) {
          await _handleTokenExpiry();
        }
        handler.next(error);
      },
    ));

    // Logging Interceptor (only in debug mode)
    if (kDebugMode) {
      _dio.interceptors.add(PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
      ));
    }
  }

  Future<void> _handleTokenExpiry() async {
    await _storage.delete(key: 'auth_token');
    // Navigate to login screen or refresh token
  }

  Dio get dio => _dio;
}
```

## 4. Data Models with JSON Serialization

### Base Response Model

```dart
// lib/data/models/api_response.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'api_response.freezed.dart';
part 'api_response.g.dart';

@Freezed(genericArgumentFactories: true)
class ApiResponse<T> with _$ApiResponse<T> {
  const factory ApiResponse({
    required int status,
    required String message,
    required T data,
  }) = _ApiResponse<T>;

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object?) fromJsonT,
  ) => _$ApiResponseFromJson(json, fromJsonT);
}
```

### User Model (handling TypeORM transformations)

```dart
// lib/data/models/user_model.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

@freezed
class UserModel with _$UserModel {
  const factory UserModel({
    required String userId,
    required String userRole,
    required String username,
    required String fullname,
    String? avatarUrl,

    // Handle TypeORM string-to-number transformation
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,

    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int friendCount,

    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int followerCount,

    required List<String> hobbies,

    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFollowing,

    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFriend,

    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFirstPerson,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);
}

// Helper functions for TypeORM transformations
int _stringToInt(dynamic value) {
  if (value is int) return value;
  if (value is String) return int.tryParse(value) ?? 0;
  return 0;
}

String _intToString(int value) => value.toString();

bool _stringToBool(dynamic value) {
  if (value is bool) return value;
  if (value is String) {
    return value.toLowerCase() == 'true' || value == '1' || value == 't';
  }
  return false;
}

String _boolToString(bool value) => value.toString();
```

### Ivent Model

```dart
// lib/data/models/ivent_model.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'ivent_model.freezed.dart';
part 'ivent_model.g.dart';

@freezed
class IventCardModel with _$IventCardModel {
  const factory IventCardModel({
    required String iventId,
    required String iventName,
    String? thumbnailUrl,
    required String locationName,
    required String creatorId,
    required AccountType creatorType,
    required String creatorName,
    String? creatorImageUrl,

    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    bool? isFavorited,

    required IventViewType viewType,
  }) = _IventCardModel;

  factory IventCardModel.fromJson(Map<String, dynamic> json) =>
      _$IventCardModelFromJson(json);
}

enum AccountType {
  @JsonValue('USER')
  user,
  @JsonValue('PAGE')
  page,
}

enum IventViewType {
  @JsonValue('CARD')
  card,
  @JsonValue('LIST')
  list,
}
```

## 5. API Service Classes

### User Service

```dart
// lib/data/services/user_service.dart
import 'package:dio/dio.dart';
import '../models/api_response.dart';
import '../models/user_model.dart';
import 'api_client.dart';

class UserService {
  final ApiClient _apiClient;

  UserService(this._apiClient);

  Future<ApiResponse<UserModel>> getUserById(String userId) async {
    try {
      final response = await _apiClient.dio.get('/users/$userId');

      return ApiResponse.fromJson(
        response.data,
        (json) => UserModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Future<ApiResponse<RegisterReturn>> register(RegisterDto dto) async {
    try {
      final response = await _apiClient.dio.post(
        '/users/register',
        data: dto.toJson(),
      );

      return ApiResponse.fromJson(
        response.data,
        (json) => RegisterReturn.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Future<ApiResponse<void>> followUser(String userId) async {
    try {
      final response = await _apiClient.dio.post('/users/$userId/follow');

      return ApiResponse.fromJson(
        response.data,
        (json) => null,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  ApiException _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.receiveTimeout:
        return ApiException('Connection timeout');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'] ?? 'Unknown error';
        return ApiException('Server error ($statusCode): $message');
      default:
        return ApiException('Network error: ${e.message}');
    }
  }
}

class ApiException implements Exception {
  final String message;
  ApiException(this.message);

  @override
  String toString() => 'ApiException: $message';
}
```

### Ivent Service

```dart
// lib/data/services/ivent_service.dart
import 'package:dio/dio.dart';
import '../models/api_response.dart';
import '../models/ivent_model.dart';
import 'api_client.dart';

class IventService {
  final ApiClient _apiClient;

  IventService(this._apiClient);

  Future<ApiResponse<CreateIventReturn>> createIvent(CreateIventDto dto) async {
    try {
      final response = await _apiClient.dio.post(
        '/ivents/create',
        data: dto.toJson(),
      );

      return ApiResponse.fromJson(
        response.data,
        (json) => CreateIventReturn.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Future<ApiResponse<List<IventCardModel>>> getLatestIvents({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.dio.get(
        '/ivents/latest',
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );

      return ApiResponse.fromJson(
        response.data,
        (json) => (json as List)
            .map((item) => IventCardModel.fromJson(item))
            .toList(),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }
}
```

## 6. Repository Pattern Implementation

### User Repository Interface

```dart
// lib/domain/repositories/user_repository.dart
import '../entities/user_entity.dart';

abstract class UserRepository {
  Future<UserEntity> getUserById(String userId);
  Future<RegisterResult> register(RegisterRequest request);
  Future<void> followUser(String userId);
  Future<void> unfollowUser(String userId);
  Future<List<UserEntity>> searchUsers(String query);
}
```

### User Repository Implementation

```dart
// lib/data/repositories/user_repository_impl.dart
import '../../domain/entities/user_entity.dart';
import '../../domain/repositories/user_repository.dart';
import '../models/user_model.dart';
import '../services/user_service.dart';

class UserRepositoryImpl implements UserRepository {
  final UserService _userService;

  UserRepositoryImpl(this._userService);

  @override
  Future<UserEntity> getUserById(String userId) async {
    final response = await _userService.getUserById(userId);
    return _mapToEntity(response.data);
  }

  @override
  Future<RegisterResult> register(RegisterRequest request) async {
    final dto = RegisterDto(
      phoneNumber: request.phoneNumber,
      fullname: request.fullname,
      hobbyIds: request.hobbyIds,
    );

    final response = await _userService.register(dto);
    return RegisterResult(
      userId: response.data.userId,
      token: response.data.token,
      role: response.data.role,
      username: response.data.username,
      fullname: response.data.fullname,
      avatarUrl: response.data.avatarUrl,
    );
  }

  @override
  Future<void> followUser(String userId) async {
    await _userService.followUser(userId);
  }

  UserEntity _mapToEntity(UserModel model) {
    return UserEntity(
      id: model.userId,
      role: model.userRole,
      username: model.username,
      fullname: model.fullname,
      avatarUrl: model.avatarUrl,
      iventCount: model.iventCount,
      friendCount: model.friendCount,
      followerCount: model.followerCount,
      hobbies: model.hobbies,
      isFollowing: model.isFollowing,
      isFriend: model.isFriend,
      isFirstPerson: model.isFirstPerson,
    );
  }
}
```

## 7. State Management with Riverpod

### Providers Setup

```dart
// lib/presentation/providers/providers.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/user_repository_impl.dart';
import '../../data/services/api_client.dart';
import '../../data/services/user_service.dart';
import '../../domain/repositories/user_repository.dart';

// API Client Provider
final apiClientProvider = Provider<ApiClient>((ref) => ApiClient());

// Service Providers
final userServiceProvider = Provider<UserService>(
  (ref) => UserService(ref.read(apiClientProvider)),
);

// Repository Providers
final userRepositoryProvider = Provider<UserRepository>(
  (ref) => UserRepositoryImpl(ref.read(userServiceProvider)),
);

// State Providers
final userProvider = StateNotifierProvider<UserNotifier, AsyncValue<UserEntity?>>(
  (ref) => UserNotifier(ref.read(userRepositoryProvider)),
);

class UserNotifier extends StateNotifier<AsyncValue<UserEntity?>> {
  final UserRepository _userRepository;

  UserNotifier(this._userRepository) : super(const AsyncValue.data(null));

  Future<void> loadUser(String userId) async {
    state = const AsyncValue.loading();
    try {
      final user = await _userRepository.getUserById(userId);
      state = AsyncValue.data(user);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> followUser(String userId) async {
    try {
      await _userRepository.followUser(userId);
      // Refresh user data
      await loadUser(userId);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
```

## 8. Error Handling Strategy

### Custom Exception Classes

```dart
// lib/core/exceptions/api_exceptions.dart
abstract class AppException implements Exception {
  final String message;
  final String? code;

  AppException(this.message, [this.code]);
}

class NetworkException extends AppException {
  NetworkException(String message) : super(message, 'NETWORK_ERROR');
}

class ServerException extends AppException {
  final int statusCode;

  ServerException(String message, this.statusCode)
      : super(message, 'SERVER_ERROR');
}

class AuthException extends AppException {
  AuthException(String message) : super(message, 'AUTH_ERROR');
}

class ValidationException extends AppException {
  final Map<String, List<String>> errors;

  ValidationException(String message, this.errors)
      : super(message, 'VALIDATION_ERROR');
}
```

### Global Error Handler

```dart
// lib/core/error/error_handler.dart
import 'package:dio/dio.dart';
import '../exceptions/api_exceptions.dart';

class ErrorHandler {
  static AppException handleError(dynamic error) {
    if (error is DioException) {
      return _handleDioError(error);
    } else if (error is AppException) {
      return error;
    } else {
      return AppException('An unexpected error occurred');
    }
  }

  static AppException _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.sendTimeout:
        return NetworkException('Connection timeout');

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode ?? 0;
        final message = error.response?.data?['message'] ?? 'Server error';

        if (statusCode == 401) {
          return AuthException('Authentication failed');
        } else if (statusCode == 422) {
          final errors = error.response?.data?['errors'] as Map<String, dynamic>?;
          return ValidationException(message, _parseValidationErrors(errors));
        } else {
          return ServerException(message, statusCode);
        }

      case DioExceptionType.cancel:
        return AppException('Request was cancelled');

      default:
        return NetworkException('Network error occurred');
    }
  }

  static Map<String, List<String>> _parseValidationErrors(
    Map<String, dynamic>? errors,
  ) {
    if (errors == null) return {};

    return errors.map((key, value) {
      if (value is List) {
        return MapEntry(key, value.cast<String>());
      } else {
        return MapEntry(key, [value.toString()]);
      }
    });
  }
}
```

## 9. Code Generation Tools

### Automatic Model Generation Script

Create a Node.js script to generate Dart models from your TypeScript definitions:

```javascript
// scripts/generate_dart_models.js
const fs = require('fs');
const path = require('path');

// TypeScript to Dart type mapping
const typeMapping = {
  string: 'String',
  number: 'int',
  boolean: 'bool',
  Date: 'DateTime',
  'string[]': 'List<String>',
  'number[]': 'List<int>',
};

function generateDartModel(tsInterface, className) {
  const dartClass = `
import 'package:freezed_annotation/freezed_annotation.dart';

part '${className.toLowerCase()}_model.freezed.dart';
part '${className.toLowerCase()}_model.g.dart';

@freezed
class ${className}Model with _\$${className}Model {
  const factory ${className}Model({
    ${generateFields(tsInterface)}
  }) = _${className}Model;

  factory ${className}Model.fromJson(Map<String, dynamic> json) =>
      _\$${className}ModelFromJson(json);
}
`;

  return dartClass;
}

function generateFields(tsInterface) {
  // Parse TypeScript interface and generate Dart fields
  // This is a simplified example - you'd need a proper TS parser
  return tsInterface.properties
    .map((prop) => {
      const dartType = typeMapping[prop.type] || prop.type;
      const nullable = prop.optional ? '?' : '';
      const required = prop.optional ? '' : 'required ';

      return `    ${required}${dartType}${nullable} ${prop.name},`;
    })
    .join('\n');
}

// Usage example
const userInterface = {
  properties: [
    { name: 'userId', type: 'string', optional: false },
    { name: 'username', type: 'string', optional: false },
    { name: 'avatarUrl', type: 'string', optional: true },
    { name: 'iventCount', type: 'number', optional: false },
  ],
};

console.log(generateDartModel(userInterface, 'User'));
```

### Build Runner Commands

```bash
# Generate code
flutter packages pub run build_runner build

# Watch for changes
flutter packages pub run build_runner watch

# Clean generated files
flutter packages pub run build_runner clean
```

## 10. Authentication Implementation

### Auth Service

```dart
// lib/data/services/auth_service.dart
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'api_client.dart';

class AuthService {
  final ApiClient _apiClient;
  final FlutterSecureStorage _storage = const FlutterSecureStorage();

  AuthService(this._apiClient);

  Future<void> sendVerificationCode(String phoneNumber) async {
    await _apiClient.dio.post('/auth/send-verification-code', data: {
      'phoneNumber': phoneNumber,
    });
  }

  Future<ValidateResult> validateCode(String phoneNumber, String code) async {
    final response = await _apiClient.dio.post('/auth/validate', data: {
      'phoneNumber': phoneNumber,
      'verificationCode': code,
    });

    return ValidateResult.fromJson(response.data['data']);
  }

  Future<void> saveToken(String token) async {
    await _storage.write(key: 'auth_token', value: token);
  }

  Future<String?> getToken() async {
    return await _storage.read(key: 'auth_token');
  }

  Future<void> logout() async {
    await _storage.delete(key: 'auth_token');
    // Call logout endpoint if needed
  }

  Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null;
  }
}
```

## 11. File Upload Implementation

### File Upload Service

```dart
// lib/data/services/file_upload_service.dart
import 'dart:io';
import 'package:dio/dio.dart';
import 'api_client.dart';

class FileUploadService {
  final ApiClient _apiClient;

  FileUploadService(this._apiClient);

  Future<String> uploadImage(File file, {String? folder}) async {
    final fileName = file.path.split('/').last;
    final formData = FormData.fromMap({
      'file': await MultipartFile.fromFile(
        file.path,
        filename: fileName,
      ),
      if (folder != null) 'folder': folder,
    });

    final response = await _apiClient.dio.post(
      '/upload/image',
      data: formData,
      options: Options(
        headers: {'Content-Type': 'multipart/form-data'},
      ),
      onSendProgress: (sent, total) {
        // Handle upload progress
        final progress = sent / total;
        print('Upload progress: ${(progress * 100).toStringAsFixed(1)}%');
      },
    );

    return response.data['data']['url'];
  }

  Future<String> uploadVideo(File file) async {
    // Similar implementation for video upload
    // Handle larger files with chunked upload if needed
  }
}
```

## 12. Best Practices Summary

### 1. Type Safety

- Use Freezed for immutable data classes
- Implement proper JSON serialization with json_annotation
- Handle TypeORM string transformations explicitly

### 2. Error Handling

- Create custom exception hierarchy
- Implement global error handling
- Provide user-friendly error messages

### 3. Performance

- Implement pagination for list endpoints
- Use caching for frequently accessed data
- Implement proper loading states

### 4. Security

- Store tokens securely using FlutterSecureStorage
- Implement token refresh mechanism
- Validate all user inputs

### 5. Testing

- Write unit tests for repositories and services
- Mock API responses for consistent testing
- Test error scenarios

### Example Usage in Widget

```dart
// lib/presentation/screens/user_profile_screen.dart
class UserProfileScreen extends ConsumerWidget {
  final String userId;

  const UserProfileScreen({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(userProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('User Profile')),
      body: userAsync.when(
        data: (user) => user != null
            ? UserProfileView(user: user)
            : const Center(child: Text('User not found')),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('Error: ${error.toString()}'),
              ElevatedButton(
                onPressed: () => ref.read(userProvider.notifier).loadUser(userId),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

This comprehensive guide provides a solid foundation for integrating your NestJS API with Flutter, handling the specific challenges of TypeORM transformations and providing type-safe, maintainable code.
