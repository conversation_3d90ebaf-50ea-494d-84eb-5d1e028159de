import { ApiProperty } from '@nestjs/swagger';
import { AccountTypeEnum, IventViewTypeEnum } from 'src/entities';

export class IventCardItem {
  @ApiProperty({ type: 'string' })
  iventId!: string;

  @ApiProperty({ type: 'string' })
  iventName!: string;

  @ApiProperty({ type: 'string', nullable: true })
  thumbnailUrl!: string | null;

  @ApiProperty({ type: 'string' })
  locationName!: string;

  @ApiProperty({ type: 'string' })
  creatorId!: string;

  @ApiProperty({ type: 'string', enum: Object.values(AccountTypeEnum) })
  creatorType!: AccountTypeEnum;

  @ApiProperty({ type: 'string' })
  creatorName!: string;

  @ApiProperty({ type: 'string', nullable: true })
  creatorImageUrl!: string | null;

  @ApiProperty({ type: 'boolean', nullable: true })
  isFavorited!: boolean | null;

  @ApiProperty({ type: 'string', enum: Object.values(IventViewTypeEnum) })
  viewType!: IventViewTypeEnum;
}
