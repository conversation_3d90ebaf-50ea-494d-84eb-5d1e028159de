// Generated Dart model for GetUserBlocklistReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getuserblocklistreturn_model.freezed.dart';
part 'getuserblocklistreturn_model.g.dart';

@freezed
class GetUserBlocklistReturnModel with _$GetUserBlocklistReturnModel {
  const factory GetUserBlocklistReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int userCount,
  }) = _GetUserBlocklistReturnModel;

  factory GetUserBlocklistReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetUserBlocklistReturnModelFromJson(json);
}
