import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { EmptyReturn } from 'src/models/empty-return';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { DataSource } from 'typeorm';
import { NotificationsService } from '../notifications/notifications.service';
import {
  BlockUserByUserIdParams,
  GetUserBlocklistParams,
  InviteFriendByUserIdParams,
  RemoveFriendByUserIdParams,
  SearchFriendsByUserIdParams,
  UnblockUserByUserIdParams,
  UninviteFriendByUserIdParams,
} from './models/user-relationships.params';
import {
  GetUserBlocklistReturn,
  SearchFriendsByUserIdReturn,
} from './models/user-relationships.returns';

@Injectable()
export class UserRelationshipsService {
  constructor(
    private dataSource: DataSource,
    private notificationsService: NotificationsService,
  ) {}

  async getUserBlocklist(
    getUserBlocklistParams: GetUserBlocklistParams,
  ): Promise<GetUserBlocklistReturn> {
    const { sessionId, sessionRole } = getUserBlocklistParams;

    const result = await this.dataSource.query(`
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM user_relationships ur
      LEFT JOIN users u ON u.id = ur.receiver_id OR u.id = ur.sender_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      WHERE (ur.sender_id = '${sessionId} OR ur.receiver_id = '${sessionId})'
      AND ur.status = 'blocked';
    `);

    return {
      users: result.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_url,
        university: val.university,
      })),
      userCount: result.length,
    };
  }

  async blockUserByUserId(blockUserByUserIdParams: BlockUserByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId } = blockUserByUserIdParams;

    if (sessionId === userId) {
      throw new HttpException("You can't block yourself", HttpStatus.BAD_REQUEST);
    }

    const relationshipResult = await this.dataSource.query(`
      SELECT id
      FROM user_relationships
      WHERE status IN ('accepted', 'pending')
      AND ((sender_id = '${sessionId}' AND receiver_id = '${userId}') OR (sender_id = '${userId}' AND receiver_id = '${sessionId}'));  
    `);

    if (relationshipResult.length) {
      await this.dataSource
        .createQueryBuilder()
        .update('user_relationships')
        .set({ status: 'blocked' })
        .where({ id: relationshipResult[0].id })
        .execute();
    } else {
      await this.dataSource.query(
        insertQueryBuilder({
          tableName: 'user_relationships',
          values: {
            sender_id: sessionId,
            receiver_id: userId,
            status: 'blocked',
          },
        }),
      );
    }

    return {};
  }

  async unblockUserByUserId(
    unblockUserByUserIdParams: UnblockUserByUserIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId } = unblockUserByUserIdParams;

    await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('user_relationships')
      .where(
        `status = 'blocked' AND ((sender_id = '${sessionId}' AND receiver_id = '${userId}') OR (sender_id = '${userId}' AND receiver_id = '${sessionId}'))`,
      )
      .execute();

    return {};
  }

  async searchFriendsByUserId(
    searchFriendsByUserIdParams: SearchFriendsByUserIdParams,
  ): Promise<SearchFriendsByUserIdReturn> {
    const { sessionId, sessionRole, userId, type, q, limit, page } = searchFriendsByUserIdParams;

    const groupQClause = q ? `g.group_name ILIKE '${q}%'` : 'TRUE';
    const userQClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const result =
      type === 'group'
        ? await this.dataSource.query(`
      WITH MemberSummariesOfGroups AS (
          -- Summaries of their members of the groups
          SELECT
              CONCAT(ARRAY_TO_STRING((ARRAY_AGG(u.firstname))[:2], ','), '|', COUNT(u.id)) AS member_summary,
              gm.group_id AS group_id
          FROM group_memberships gm
          LEFT JOIN users u ON u.id = gm.member_id
          WHERE gm.status IN ('accepted', 'moderator', 'admin')
          AND gm.member_id != '${userId}'
          GROUP BY gm.group_id
      )
      SELECT
          g.id AS group_id,
          g.group_name AS group_name,
          msog.member_summary AS member_summary
      FROM groups g
      LEFT JOIN group_memberships gm ON gm.group_id = g.id
      LEFT JOIN MemberSummariesOfGroups msog ON msog.group_id = g.id
      WHERE member_id = '${userId}'
      AND ${groupQClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)}; 
    `)
        : await this.dataSource.query(`
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM user_friendships uf
      LEFT JOIN users u ON u.id = uf.friend_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      WHERE uf.user_id = '${userId}'
      AND ${userQClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return type === 'group'
      ? {
          groups: result.map((val) => ({
            groupId: val.group_id,
            groupName: val.group_name,
            memberNames: val.member_summary ? val.member_summary.split('|')[0].split(',') : [],
            memberCount: val.member_summary ? Number(val.member_summary.split('|')[1]) : 0,
          })),
          groupCount: result.length,
          friends: [],
          friendCount: 0,
        }
      : {
          groups: [],
          groupCount: 0,
          friends: result.map((val) => ({
            userId: val.user_id,
            username: val.username,
            avatarUrl: val.avatar_url,
            university: val.university,
          })),
          friendCount: result.length,
        };
  }

  async inviteFriendByUserId(
    inviteFriendByUserIdParams: InviteFriendByUserIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId } = inviteFriendByUserIdParams;

    if (sessionId === userId) {
      throw new HttpException("You can't add yourself", HttpStatus.BAD_REQUEST);
    }
    const sessionUserResult = await this.dataSource.query(
      `SELECT username, avatar_url FROM users WHERE id = '${sessionId}'`,
    );

    const relationshipResult = await this.dataSource.query(`
      SELECT id
      FROM user_relationships
      WHERE status IN ('accepted', 'pending', 'blocked')
      AND ((sender_id = '${sessionId}' AND receiver_id = '${userId}') OR (sender_id = '${userId}' AND receiver_id = '${sessionId}'));  
    `);
    if (relationshipResult.length) {
      throw new HttpException('User is already added, invited or blocked', HttpStatus.BAD_REQUEST);
    }

    const userRelationshipInsertResult = await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'user_relationships',
        values: {
          sender_id: sessionId,
          receiver_id: userId,
          status: 'pending',
        },
      }),
    );

    // Create notification
    await this.notificationsService.sendNotifications(
      {
        notification_type: 'type_1',
        account_type: 'user',
        account_id: sessionId,
        account_name: sessionUserResult[0].username,
        account_image_url: sessionUserResult[0].avatar_url,
        subject_id: userRelationshipInsertResult[0].id,
      },
      [userId],
    );

    return {};
  }

  async uninviteFriendByUserId(
    uninviteFriendByUserIdParams: UninviteFriendByUserIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId } = uninviteFriendByUserIdParams;

    const relationshipResult = await this.dataSource.query(`
      SELECT id
      FROM user_relationships
      WHERE status = 'pending'
      AND ((sender_id = '${sessionId}' AND receiver_id = '${userId}') OR (sender_id = '${userId}' AND receiver_id = '${sessionId}'));  
    `);
    if (relationshipResult.length) {
      throw new HttpException('User is not invited', HttpStatus.BAD_REQUEST);
    }

    await this.dataSource
      .createQueryBuilder()
      .update('user_relationships')
      .set({ status: 'cancelled' })
      .where({ id: relationshipResult[0].id })
      .execute();

    return {};
  }

  async removeFriendByUserId(
    removeFriendByUserIdParams: RemoveFriendByUserIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId } = removeFriendByUserIdParams;

    const blockedRelationshipResult = await this.dataSource.query(`
      SELECT id
      FROM user_relationships
      WHERE status = 'blocked'
      AND ((sender_id = '${sessionId}' AND receiver_id = '${userId}') OR (sender_id = '${userId}' AND receiver_id = '${sessionId}'));  
    `);

    if (blockedRelationshipResult.length) {
      throw new HttpException('User is blocked', HttpStatus.BAD_REQUEST);
    }

    await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('user_relationships')
      .where(
        `status = 'accepted' AND ((sender_id = '${sessionId}' AND receiver_id = '${userId}') OR (sender_id = '${userId}' AND receiver_id = '${sessionId}'))`,
      )
      .execute();

    return {};
  }
}
