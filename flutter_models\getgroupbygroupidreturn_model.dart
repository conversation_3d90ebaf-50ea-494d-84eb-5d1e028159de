// Generated Dart model for GetGroupByGroupIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getgroupbygroupidreturn_model.freezed.dart';
part 'getgroupbygroupidreturn_model.g.dart';

@freezed
class GetGroupByGroupIdReturnModel with _$GetGroupByGroupIdReturnModel {
  const factory GetGroupByGroupIdReturnModel({
    required String groupId,
    required String groupName,
    required String thumbnailUrl,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int memberCount,
  }) = _GetGroupByGroupIdReturnModel;

  factory GetGroupByGroupIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetGroupByGroupIdReturnModelFromJson(json);
}
