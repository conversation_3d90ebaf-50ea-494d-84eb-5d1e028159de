// Generated Dart model for GetPageDetailsByPageIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'getpagedetailsbypageidreturn_model.freezed.dart';
part 'getpagedetailsbypageidreturn_model.g.dart';

@freezed
class GetPageDetailsByPageIdReturnModel with _$GetPageDetailsByPageIdReturnModel {
  const factory GetPageDetailsByPageIdReturnModel({
    String? description,
    String? websiteUrl,
    required String locationId,
    String? locationAdress,
  }) = _GetPageDetailsByPageIdReturnModel;

  factory GetPageDetailsByPageIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetPageDetailsByPageIdReturnModelFromJson(json);
}
