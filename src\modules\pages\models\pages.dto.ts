import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsOptional, IsString, IsUUID, IsUrl } from 'class-validator';

export class CreatePageDto {
  @ApiProperty({ type: 'string' })
  @IsString()
  pageName!: string;

  @ApiProperty({ type: 'string', nullable: true })
  @IsUrl()
  @IsOptional()
  thumbnailUrl!: string | null;

  @ApiProperty({ type: 'string', nullable: true })
  @IsUrl()
  @IsOptional()
  websiteUrl!: string | null;

  @ApiProperty({ type: 'string', nullable: true })
  @IsString()
  @IsOptional()
  description!: string | null;

  @ApiProperty({ type: 'boolean' })
  @IsBoolean()
  isEdu!: boolean;

  @ApiProperty({ type: 'boolean' })
  @IsBoolean()
  haveMembership!: boolean;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
  })
  @IsArray()
  @IsString({ each: true })
  tags!: string[];

  @ApiProperty({ type: 'array', items: { type: 'string' } })
  @IsArray()
  @IsUUID('4', { each: true })
  creatorIds!: string[];

  @ApiProperty({ type: 'string' })
  @IsUUID('4')
  locationId!: string;
}

export class RemoveFollowerByPageIdDto {
  @ApiProperty({ type: 'string' })
  @IsUUID('4')
  userId!: string;
}

export class UpdateDescriptionByPageIdDto {
  @ApiProperty({ type: 'string' })
  @IsString()
  newDescription!: string;
}

export class UpdateLinksByPageIdDto {
  @ApiProperty({ type: 'string' })
  @IsUrl()
  newLink!: string;
}

export class UpdateLocationByPageIdDto {
  @ApiProperty({ type: 'string' })
  @IsUUID('4')
  newLocationId!: string;
}
