// Generated Dart model for SendVerificationCodeDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'sendverificationcodedto_model.freezed.dart';
part 'sendverificationcodedto_model.g.dart';

@freezed
class SendVerificationCodeDtoModel with _$SendVerificationCodeDtoModel {
  const factory SendVerificationCodeDtoModel({
    required String phoneNumber,
  }) = _SendVerificationCodeDtoModel;

  factory SendVerificationCodeDtoModel.fromJson(Map<String, dynamic> json) =>
      _$SendVerificationCodeDtoModelFromJson(json);
}
