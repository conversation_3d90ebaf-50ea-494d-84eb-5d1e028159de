// Generated Dart model for ValidateDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'validatedto_model.freezed.dart';
part 'validatedto_model.g.dart';

@freezed
class ValidateDtoModel with _$ValidateDtoModel {
  const factory ValidateDtoModel({
    required String validationCode,
    required String phoneNumber,
  }) = _ValidateDtoModel;

  factory ValidateDtoModel.fromJson(Map<String, dynamic> json) =>
      _$ValidateDtoModelFromJson(json);
}
