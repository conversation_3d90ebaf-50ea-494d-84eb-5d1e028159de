// Generated Dart model for InviteMembersByGroupIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'invitemembersbygroupiddto_model.freezed.dart';
part 'invitemembersbygroupiddto_model.g.dart';

@freezed
class InviteMembersByGroupIdDtoModel with _$InviteMembersByGroupIdDtoModel {
  const factory InviteMembersByGroupIdDtoModel({
    required List<String> userIds,
  }) = _InviteMembersByGroupIdDtoModel;

  factory InviteMembersByGroupIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$InviteMembersByGroupIdDtoModelFromJson(json);
}
