// Generated Dart model for UpdateDescriptionByPageIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'updatedescriptionbypageiddto_model.freezed.dart';
part 'updatedescriptionbypageiddto_model.g.dart';

@freezed
class UpdateDescriptionByPageIdDtoModel with _$UpdateDescriptionByPageIdDtoModel {
  const factory UpdateDescriptionByPageIdDtoModel({
    required String newDescription,
  }) = _UpdateDescriptionByPageIdDtoModel;

  factory UpdateDescriptionByPageIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$UpdateDescriptionByPageIdDtoModelFromJson(json);
}
