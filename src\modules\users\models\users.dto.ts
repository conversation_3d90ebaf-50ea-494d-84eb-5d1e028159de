import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsUUID, IsUrl, Matches } from 'class-validator';
import { UserEduVerificationEnum } from 'src/entities';

export class RegisterDto {
  @ApiProperty({ type: 'string', example: '+90(500)4003020' })
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/)
  phoneNumber!: string;

  @ApiProperty({
    type: 'string',
    description: "An user's full name can only contain letters.",
  })
  @Matches(/^[a-zA-Z ]*$/)
  fullname!: string;

  @ApiProperty({ type: 'array', items: { type: 'string' } })
  @IsArray()
  @IsUUID('4', { each: true })
  hobbyIds!: string[];
}

export class GetContactsByUserIdDto {
  @ApiProperty({ type: 'array', items: { type: 'string' }, example: '+90(500)4003020' })
  @IsArray()
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, { each: true })
  phoneNumbers!: string[];
}

export class RemoveFollowerByUserIdDto {
  @ApiProperty({ type: 'string' })
  @IsUUID('4')
  followerId!: string;
}

export class SavePhoneContactsDto {
  @ApiProperty({ type: 'array', items: { type: 'string', example: '+90(500)4003020' } })
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, { each: true })
  @IsArray()
  phoneNumbers!: string[];
}

export class SendCreatorRequestFormDto {}

export class SendVerificationEmailDto {}

export class UpdateByUserIdDto {
  @ApiProperty({
    type: 'string',
    description: 'An user name can only contain letters, numbers, underscores, and hyphens.',
  })
  @Matches(/^[a-zA-Z0-9_\-]{4,20}$/)
  newUsername!: string;

  @ApiProperty({ type: 'string' })
  @Matches(/^\d{4}-\d{2}-\d{2}$/)
  newBirthday!: string;

  @ApiProperty({ type: 'string' })
  @IsEnum(['male', 'female', 'other'])
  newGender!: string;

  @ApiProperty({ type: 'string' })
  @IsUrl()
  newAvatarUrl!: string;
}

export class UpdateEmailByUserIdDto {
  @ApiProperty({ type: 'string' })
  @Matches(/^\d{4}-\d{2}-\d{2}$/)
  newEmail!: string;
}

export class UpdateGradByUserIdDto {
  @ApiProperty({ type: 'string', enum: Object.values(UserEduVerificationEnum) })
  @IsEnum(UserEduVerificationEnum)
  newGrad!: UserEduVerificationEnum;
}

export class UpdateNotificationsByUserIdDto {}

export class UpdatePhoneNumberByUserIdDto {
  @ApiProperty({ type: 'string', example: '+90(500)4003020' })
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/)
  newPhoneNumber!: string;
}

export class UpdatePrivacyByUserIdDto {}

export class ValidateEmailDto {}
