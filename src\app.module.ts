import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as fs from 'fs';
import { AppController } from './app.controller';
import * as entities from './entities';
import { AppLoggerMiddleware } from './middlewares/app-logger-middleware';
import { TokenMiddleware } from './middlewares/token-middleware';
import { AuthModule } from './modules/auth/auth.module';
import { CommentsModule } from './modules/comments/comments.module';
import { FirebaseModule } from './modules/firebase/firebase-storage.module';
import { GroupMembershipsModule } from './modules/group-memberships/group-memberships.module';
import { GroupsModule } from './modules/groups/groups.module';
import { HobbiesModule } from './modules/hobbies/hobbies.module';
import { HomeModule } from './modules/home/<USER>';
import { IventCollabsModule } from './modules/ivent-collabs/ivent-collabs.module';
import { IventsModule } from './modules/ivents/ivents.module';
import { LocationsModule } from './modules/locations/locations.module';
import { MemoriesModule } from './modules/memories/memories.module';
import { MemoryFoldersModule } from './modules/memory-folders/memory-folders.module';
import { NotificationsModule } from './modules/notifications/notifications.module';
import { PageBlacklistsModule } from './modules/page-blacklists/page-blacklists.module';
import { PageMembershipsModule } from './modules/page-memberships/page-memberships.module';
import { PagesModule } from './modules/pages/pages.module';
import { SquadMembershipsModule } from './modules/squad-memberships/squad-memberships.module';
import { SquadsModule } from './modules/squads/squads.module';
import { UniversitiesModule } from './modules/universities/universities.module';
import { UserRelationshipsModule } from './modules/user-relationships/user-relationships.module';
import { UsersModule } from './modules/users/users.module';
import { VibeFoldersModule } from './modules/vibe-folders/vibe-folders.module';
import { VibesModule } from './modules/vibes/vibes.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        type: 'postgres',
        username: configService.get<string>('POSTGRES_USER')!,
        password: configService.get<string>('POSTGRES_PASSWORD')!,
        database: configService.get<string>('POSTGRES_DB')!,
        host: configService.get<string>('POSTGRES_HOST')!,
        port: configService.get<number>('POSTGRES_PORT')!,
        ssl: {
          rejectUnauthorized: false,
          ca: fs.readFileSync('ca.pem').toString(),
        },
        retryAttempts: 1,
        entities: [
          entities.Auth,
          entities.Comment,
          entities.Distributor,
          entities.GroupMembership,
          entities.Group,
          entities.Hobby,
          entities.IventCollab,
          entities.IventDate,
          entities.IventGroup,
          entities.IventTag,
          entities.IventUniversity,
          entities.Ivent,
          entities.Location,
          entities.MemoryFolder,
          entities.Memory,
          entities.Notification,
          entities.PageBlacklist,
          entities.PageBlockByUser,
          entities.PageFollower,
          entities.PageMembership,
          entities.PageSubscriber,
          entities.PageTag,
          entities.Page,
          entities.SquadMembership,
          entities.Squad,
          entities.University,
          entities.UserContact,
          entities.UserFavorite,
          entities.UserFollower,
          entities.UserHobby,
          entities.UserNotification,
          entities.UserRelationship,
          entities.UserSubscriber,
          entities.User,
          entities.VibeFolder,
          entities.VibeLike,
          entities.VibeView,
          entities.Vibe,
          // View entities (with dependency order)
          entities.IventUsers,
          entities.UserFriendships,
          entities.SquadFriendships,
          entities.IventDatesAggregated,
          entities.IventTagsAggregated,
          entities.UserProfileStats,
          entities.VibeRankings,
          entities.ActiveSessionIvents,
          entities.CollabSummaryOfIvent,
          entities.MemberSummaryOfSessionSquad,
          entities.ParticipantSummaryOfIvent,
          entities.SessionFriendSummaryOfIvent,
        ],
        logging: true,
      }),
      inject: [ConfigService],
    }),
    AuthModule,
    CommentsModule,
    FirebaseModule,
    GroupMembershipsModule,
    GroupsModule,
    HobbiesModule,
    HomeModule,
    IventCollabsModule,
    IventsModule,
    LocationsModule,
    MemoriesModule,
    MemoryFoldersModule,
    NotificationsModule,
    PageBlacklistsModule,
    PageMembershipsModule,
    PagesModule,
    SquadMembershipsModule,
    SquadsModule,
    UniversitiesModule,
    UserRelationshipsModule,
    UsersModule,
    VibeFoldersModule,
    VibesModule,
  ],
  controllers: [AppController],
  providers: [],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes('*');
    consumer.apply(AppLoggerMiddleware).forRoutes('*');
  }
}
