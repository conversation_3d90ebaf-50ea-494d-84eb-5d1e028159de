// Generated Dart model for SavePhoneContactsDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'savephonecontactsdto_model.freezed.dart';
part 'savephonecontactsdto_model.g.dart';

@freezed
class SavePhoneContactsDtoModel with _$SavePhoneContactsDtoModel {
  const factory SavePhoneContactsDtoModel({
    required List<String> phoneNumbers,
  }) = _SavePhoneContactsDtoModel;

  factory SavePhoneContactsDtoModel.fromJson(Map<String, dynamic> json) =>
      _$SavePhoneContactsDtoModelFromJson(json);
}
