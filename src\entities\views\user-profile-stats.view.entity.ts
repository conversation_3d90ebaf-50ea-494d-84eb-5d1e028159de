import { stringToArrayTransformer, stringToNumberTransformer } from 'src/utils/value-transformers';
import { Jo<PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, ViewColumn, ViewEntity } from 'typeorm';
import { User } from '../user.entity';
import { UserFriendships } from './user-friendships.view.entity';

@ViewEntity({
  name: 'user_profile_stats',
  expression: `
    WITH
        user_ivent_stats AS (
            SELECT count(ivent_users.ivent_id) AS ivent_count, ivent_users.account_id
            FROM ivent_users
            WHERE
                ivent_users.status IN ('accepted', 'joined', 'admin')
            GROUP BY
                ivent_users.account_id
        ),
        user_friend_stats AS (
            SELECT count(user_friendships.friend_id) AS friend_count, user_friendships.user_id
            FROM user_friendships
            GROUP BY
                user_friendships.user_id
        ),
        user_follower_stats AS (
            SELECT count(user_followers.follower_id) AS follower_count, user_followers.following_id
            FROM user_followers
            GROUP BY
                user_followers.following_id
        ),
        user_hobby_list AS (
            SELECT string_agg(DISTINCT h.hobby_name, ',') AS hobbies, uh.user_id
            FROM user_hobbies uh
                LEFT JOIN hobbies h ON h.id = uh.hobby_id
            GROUP BY
                uh.user_id
        )
    SELECT
        u.id AS user_id,
        concat(u.firstname, ' ', u.lastname) AS fullname,
        COALESCE(uis.ivent_count, 0) AS ivent_count,
        COALESCE(ufrs.friend_count, 0) AS friend_count,
        COALESCE(ufos.follower_count, 0) AS follower_count,
        COALESCE(uhl.hobbies, '') AS hobbies
    FROM
        public.users u
        LEFT JOIN user_ivent_stats uis ON uis.account_id = u.id
        LEFT JOIN user_friend_stats ufrs ON ufrs.user_id = u.id
        LEFT JOIN user_follower_stats ufos ON ufos.following_id = u.id
        LEFT JOIN user_hobby_list uhl ON uhl.user_id = u.id
  `,
  dependsOn: [() => UserFriendships],
})
export class UserProfileStats {
  @ViewColumn()
  user_id!: string;

  @OneToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @ViewColumn()
  fullname!: string;

  @ViewColumn({ transformer: stringToNumberTransformer })
  ivent_count!: number;

  @ViewColumn({ transformer: stringToNumberTransformer })
  friend_count!: number;

  @ViewColumn({ transformer: stringToNumberTransformer })
  follower_count!: number;

  @ViewColumn({ transformer: stringToArrayTransformer })
  hobbies!: string[];
}
