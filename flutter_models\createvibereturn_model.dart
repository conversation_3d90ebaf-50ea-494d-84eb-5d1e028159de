// Generated Dart model for CreateVibeReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'createvibereturn_model.freezed.dart';
part 'createvibereturn_model.g.dart';

@freezed
class CreateVibeReturnModel with _$CreateVibeReturnModel {
  const factory CreateVibeReturnModel({
    required String vibeId,
  }) = _CreateVibeReturnModel;

  factory CreateVibeReturnModel.fromJson(Map<String, dynamic> json) =>
      _$CreateVibeReturnModelFromJson(json);
}
