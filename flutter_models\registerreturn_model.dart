// Generated Dart model for RegisterReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'registerreturn_model.freezed.dart';
part 'registerreturn_model.g.dart';

@freezed
class RegisterReturnModel with _$RegisterReturnModel {
  const factory RegisterReturnModel({
    required String userId,
    required String token,
    required String role,
    required String username,
    required String fullname,
    String? avatarUrl,
  }) = _RegisterReturnModel;

  factory RegisterReturnModel.fromJson(Map<String, dynamic> json) =>
      _$RegisterReturnModelFromJson(json);
}
