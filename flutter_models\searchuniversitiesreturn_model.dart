// Generated Dart model for SearchUniversitiesReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchuniversitiesreturn_model.freezed.dart';
part 'searchuniversitiesreturn_model.g.dart';

@freezed
class SearchUniversitiesReturnModel with _$SearchUniversitiesReturnModel {
  const factory SearchUniversitiesReturnModel({
    required List<Map<String, dynamic>> universities,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int universityCount,
  }) = _SearchUniversitiesReturnModel;

  factory SearchUniversitiesReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchUniversitiesReturnModelFromJson(json);
}
