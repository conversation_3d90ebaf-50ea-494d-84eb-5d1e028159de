// Generated Dart model for GetIventPageByIventIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getiventpagebyiventidreturn_model.freezed.dart';
part 'getiventpagebyiventidreturn_model.g.dart';

@freezed
class GetIventPageByIventIdReturnModel with _$GetIventPageByIventIdReturnModel {
  const factory GetIventPageByIventIdReturnModel({
    required String iventId,
    required String iventName,
    String? thumbnailUrl,
    required String locationId,
    required String mapboxId,
    required String locationName,
    required List<String> date,
    String? description,
    required String categoryTag,
    required List<String> tags,
    required String creatorId,
    required String creatorType,
    required String creatorName,
    String? creatorImageUrl,
    required List<String> collabNames,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int collabCount,
    List<String>? memberNames,
    List<String>? memberAvatarUrls,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int memberCount,
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    bool? isFavorited,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    int? favoriteCount,
    String? googleFormsUrl,
    String? instagramUsername,
    String? whatsappUrl,
    String? whatsappNumber,
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    bool? isWhatsappUrlPrivate,
    String? callNumber,
    String? websiteUrl,
    required String viewType,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,
    required List<Map<String, dynamic>> images,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int imageCount,
    required String iventId,
    required String iventName,
    required String date,
    required List<String> memberNames,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int memberCount,
  }) = _GetIventPageByIventIdReturnModel;

  factory GetIventPageByIventIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetIventPageByIventIdReturnModelFromJson(json);
}
