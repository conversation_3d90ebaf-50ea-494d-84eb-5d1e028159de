import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, ViewColumn, ViewEntity } from 'typeorm';
import { IventCollab } from '../ivent-collab.entity';
import { Ivent } from '../ivent.entity';
import { Page } from '../page.entity';
import { SquadMembership } from '../squad-membership.entity';
import { User } from '../user.entity';
import { IventUserStatusEnum, IventUserTypeEnum } from './enums';

@ViewEntity({
  name: 'ivent_users',
  expression: `
    SELECT
        i.id AS ivent_id,
        COALESCE(ic.id, sm.squad_id) AS id,
        COALESCE(
            ic.collab_page_id,
            ic.collab_user_id,
            sm.member_id
        ) AS account_id,
        (
            CASE
                WHEN (ic.collab_page_id IS NOT NULL) THEN 'page'
                WHEN (ic.collab_user_id IS NOT NULL) THEN 'user'
                WHEN (sm.member_id IS NOT NULL) THEN 'member'
            END
        )::public.ivent_user_type_enum AS type,
        COALESCE(
            ic.status::TEXT,
            sm.status::TEXT
        )::public.ivent_user_status_enum AS status,
        COALESCE(ic.inviter_id, sm.inviter_id) AS inviter_id
    FROM public.ivents i
        LEFT JOIN public.ivent_collabs ic ON ic.ivent_id = i.id
        LEFT JOIN public.squads s ON s.ivent_id = i.id
        LEFT JOIN public.squad_memberships sm ON sm.squad_id = s.id
    WHERE
        COALESCE(
            ic.collab_page_id,
            ic.collab_user_id,
            sm.member_id
        ) IS NOT NULL
  `,
})
export class IventUsers {
  @ViewColumn()
  ivent_id!: string;

  @ManyToOne(() => Ivent)
  @JoinColumn({ name: 'ivent_id' })
  ivent?: Ivent;

  @ViewColumn()
  id!: string;

  @ManyToOne(() => IventCollab, { nullable: true })
  @JoinColumn({ name: 'id' })
  ivent_collab?: IventCollab | null;

  @ManyToOne(() => SquadMembership, { nullable: true })
  @JoinColumn({ name: 'id' })
  squad_membership?: SquadMembership | null;

  @ViewColumn()
  account_id!: string;

  @ManyToOne(() => Page, { nullable: true })
  @JoinColumn({ name: 'account_id' })
  page?: Page | null;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'account_id' })
  user?: User | null;

  @ViewColumn()
  inviter_id!: string | null;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'inviter_id' })
  inviter?: User | null;

  @ViewColumn()
  type!: IventUserTypeEnum;

  @ViewColumn()
  status!: IventUserStatusEnum;
}
