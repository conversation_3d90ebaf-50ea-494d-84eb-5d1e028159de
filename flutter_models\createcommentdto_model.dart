// Generated Dart model for CreateCommentDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'createcommentdto_model.freezed.dart';
part 'createcommentdto_model.g.dart';

@freezed
class CreateCommentDtoModel with _$CreateCommentDtoModel {
  const factory CreateCommentDtoModel({
    required String comment,
    required String vibeId,
    required String iventName,
    String? thumbnailUrl,
    required String creatorId,
  }) = _CreateCommentDtoModel;

  factory CreateCommentDtoModel.fromJson(Map<String, dynamic> json) =>
      _$CreateCommentDtoModelFromJson(json);
}
