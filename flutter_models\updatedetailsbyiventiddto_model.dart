// Generated Dart model for UpdateDetailsByIventIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'updatedetailsbyiventiddto_model.freezed.dart';
part 'updatedetailsbyiventiddto_model.g.dart';

@freezed
class UpdateDetailsByIventIdDtoModel with _$UpdateDetailsByIventIdDtoModel {
  const factory UpdateDetailsByIventIdDtoModel({
    required String newDetails,
  }) = _UpdateDetailsByIventIdDtoModel;

  factory UpdateDetailsByIventIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$UpdateDetailsByIventIdDtoModelFromJson(json);
}
