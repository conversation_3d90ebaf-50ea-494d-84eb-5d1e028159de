import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { UserRelationshipStatusEnum } from 'src/entities';
import { UserListItem } from 'src/models/user-list-item';

export class SearchInvitableUsersByIventIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      type: 'object',
      properties: {
        groupId: { type: 'string' },
        groupName: { type: 'string' },
        thumbnailUrl: { type: 'string' },
        memberNames: {
          type: 'array',
          items: { type: 'boolean' },
        },
        memberCount: { type: 'number' },
      },
    },
  })
  groups!: {
    groupId: string;
    groupName: string;
    thumbnailUrl: string;
    memberNames: string[];
    memberCount: number;
  }[];

  @ApiProperty({ type: 'number' })
  groupCount!: number;

  @ApiProperty({
    type: 'array',
    items: {
      allOf: [
        { $ref: getSchemaPath(UserListItem) },
        {
          type: 'object',
          properties: {
            firstname: { type: 'string' },
          },
        },
      ],
    },
  })
  friends!: (UserListItem & { firstname: string })[];

  @ApiProperty({ type: 'number' })
  friendCount!: number;
}

export class SearchParticipantsByIventIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      allOf: [
        { $ref: getSchemaPath(UserListItem) },
        {
          type: 'object',
          properties: {
            isFriend: { type: 'boolean' },
            type: { type: 'string' },
          },
        },
      ],
    },
  })
  users!: (UserListItem & { relationship: UserRelationshipStatusEnum; type: string })[];

  @ApiProperty({ type: 'number' })
  userCount!: number;

  @ApiProperty({ type: 'string' })
  viewType!: string;
}
