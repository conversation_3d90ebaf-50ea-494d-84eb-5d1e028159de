import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { UserListItem } from 'src/models/user-list-item';

export class CreateGroupReturn {
  @ApiProperty({ type: 'string' })
  groupId!: string;
}

export class GetGroupByGroupIdReturn {
  @ApiProperty({ type: 'string' })
  groupId!: string;

  @ApiProperty({ type: 'string' })
  groupName!: string;

  @ApiProperty({ type: 'string' })
  thumbnailUrl!: string;

  @ApiProperty({
    allOf: [
      { $ref: getSchemaPath(UserListItem) },
      {
        type: 'object',
        properties: { role: { type: 'string' }, isFriend: { type: 'boolean' } },
      },
    ],
  })
  members!: (UserListItem & { role: string; isFriend: boolean })[];

  @ApiProperty({ type: 'number' })
  memberCount!: number;
}
