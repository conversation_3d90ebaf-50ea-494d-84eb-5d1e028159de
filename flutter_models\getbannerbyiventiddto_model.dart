// Generated Dart model for GetBannerByIventIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'getbannerbyiventiddto_model.freezed.dart';
part 'getbannerbyiventiddto_model.g.dart';

@freezed
class GetBannerByIventIdDtoModel with _$GetBannerByIventIdDtoModel {
  const factory GetBannerByIventIdDtoModel({
    required List<String> iventIds,
  }) = _GetBannerByIventIdDtoModel;

  factory GetBannerByIventIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$GetBannerByIventIdDtoModelFromJson(json);
}
