import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { IventListItem } from 'src/models/ivent-list-item';
import { MemoryFolderCardItem } from 'src/models/memory-folder-card-item';
import { UserListItem } from 'src/models/user-list-item';
import { VibeFolderCardItem } from 'src/models/vibe-folder-card-item';

export class GetContactsByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      allOf: [
        { $ref: getSchemaPath(UserListItem) },
        {
          type: 'object',
          properties: { phoneNumber: { type: 'string' } },
        },
      ],
    },
  })
  contacts!: (UserListItem & { phoneNumber: string })[];

  @ApiProperty({ type: 'number' })
  contactCount!: number;
}

export class GetFavoritesByUserIdReturn {
  @ApiProperty({ type: 'array', items: { $ref: getSchemaPath(IventListItem) } })
  ivents!: IventListItem[];

  @ApiProperty({ type: 'number' })
  iventCount!: number;
}

export class GetFollowerFriendsByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(UserListItem) },
  })
  friends!: UserListItem[];

  @ApiProperty({ type: 'number' })
  friendCount!: number;
}

export class GetFollowersByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
  })
  friendFirstnames!: string[];

  @ApiProperty({ type: 'number' })
  friendCount!: number;

  @ApiProperty({
    type: 'array',
    items: {
      allOf: [
        { $ref: getSchemaPath(UserListItem) },
        {
          type: 'object',
          properties: { isFriend: { type: 'boolean' } },
        },
      ],
    },
  })
  followers!: (UserListItem & { isFriend: boolean })[];

  @ApiProperty({ type: 'number' })
  followerCount!: number;

  @ApiProperty({ type: 'boolean' })
  isFirstPerson!: boolean;
}

export class GetFollowingsByUserIdReturn {
  @ApiProperty({ type: 'array', items: { $ref: getSchemaPath(UserListItem) } })
  followings!: UserListItem[];

  @ApiProperty({ type: 'number' })
  followingCount!: number;
}

export class GetIventsByUserIdReturn {
  @ApiProperty({ type: 'array', items: { $ref: getSchemaPath(IventListItem) } })
  ivents!: IventListItem[];

  @ApiProperty({ type: 'number' })
  iventCount!: number;

  @ApiProperty({ type: 'boolean' })
  isFirstPerson!: boolean;
}

export class GetLevelByUserIdReturn {
  @ApiProperty({ type: 'string' })
  levelInfo!: string;
}

export class GetPagesByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      type: 'object',
      properties: {
        pageId: { type: 'string' },
        pageRole: { type: 'string' },
        pageName: { type: 'string' },
        thumbnailUrl: { type: 'string' },
      },
    },
  })
  pages!: {
    pageId: string;
    pageRole: string;
    pageName: string;
    thumbnailUrl: string;
  };

  @ApiProperty({ type: 'number' })
  pageCount!: number;
}

export class GetMemoryFoldersByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(MemoryFolderCardItem) },
  })
  memoryFolders!: MemoryFolderCardItem[];

  @ApiProperty({ type: 'number' })
  memoryFolderCount!: number;
}

export class GetUserByUserIdReturn {
  @ApiProperty({ type: 'string' })
  userId!: string;

  @ApiProperty({ type: 'string' })
  userRole!: string;

  @ApiProperty({ type: 'string' })
  username!: string;

  @ApiProperty({ type: 'string' })
  fullname!: string;

  @ApiProperty({ type: 'string', nullable: true })
  avatarUrl!: string | null;

  @ApiProperty({ type: 'number' })
  iventCount!: number;

  @ApiProperty({ type: 'number' })
  friendCount!: number;

  @ApiProperty({ type: 'number' })
  followerCount!: number;

  @ApiProperty({ type: 'array', items: { type: 'string' } })
  hobbies!: string[];

  @ApiProperty({ type: 'boolean' })
  isFollowing!: boolean;

  @ApiProperty({ type: 'boolean' })
  isFriend!: boolean;

  @ApiProperty({ type: 'boolean' })
  isFirstPerson!: boolean;
}

export class GetVibeFoldersByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(VibeFolderCardItem) },
  })
  vibeFolders!: VibeFolderCardItem[];

  @ApiProperty({ type: 'number' })
  vibeFolderCount!: number;
}

export class RegisterReturn {
  @ApiProperty({ type: 'string' })
  userId!: string;

  @ApiProperty({ type: 'string' })
  token!: string;

  @ApiProperty({ type: 'string' })
  role!: string;

  @ApiProperty({ type: 'string' })
  username!: string;

  @ApiProperty({ type: 'string' })
  fullname!: string;

  @ApiProperty({ type: 'string', nullable: true })
  avatarUrl!: string | null;
}

export class GetUserBannerByUserIdReturn {
  @ApiProperty({ type: 'string' })
  userId!: string;

  @ApiProperty({ type: 'string' })
  username!: string;

  @ApiProperty({ type: 'string', nullable: true })
  avatarUrl!: string | null;

  @ApiProperty({ type: 'string' })
  fullname!: string;
}
