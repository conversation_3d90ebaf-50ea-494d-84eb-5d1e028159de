// Generated Dart model for UpdateLocationByPageIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'updatelocationbypageiddto_model.freezed.dart';
part 'updatelocationbypageiddto_model.g.dart';

@freezed
class UpdateLocationByPageIdDtoModel with _$UpdateLocationByPageIdDtoModel {
  const factory UpdateLocationByPageIdDtoModel({
    required String newLocationId,
  }) = _UpdateLocationByPageIdDtoModel;

  factory UpdateLocationByPageIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$UpdateLocationByPageIdDtoModelFromJson(json);
}
