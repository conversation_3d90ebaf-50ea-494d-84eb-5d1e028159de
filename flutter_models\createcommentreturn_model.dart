// Generated Dart model for CreateCommentReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'createcommentreturn_model.freezed.dart';
part 'createcommentreturn_model.g.dart';

@freezed
class CreateCommentReturnModel with _$CreateCommentReturnModel {
  const factory CreateCommentReturnModel({
    required String commentId,
  }) = _CreateCommentReturnModel;

  factory CreateCommentReturnModel.fromJson(Map<String, dynamic> json) =>
      _$CreateCommentReturnModelFromJson(json);
}
