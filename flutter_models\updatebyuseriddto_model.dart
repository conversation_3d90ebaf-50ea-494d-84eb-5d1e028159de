// Generated Dart model for UpdateByUserIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'updatebyuseriddto_model.freezed.dart';
part 'updatebyuseriddto_model.g.dart';

@freezed
class UpdateByUserIdDtoModel with _$UpdateByUserIdDtoModel {
  const factory UpdateByUserIdDtoModel({
    required String newUsername,
    required String newBirthday,
    required String newGender,
    required String newAvatarUrl,
    required String newEmail,
    required String newGrad,
    required String newPhoneNumber,
  }) = _UpdateByUserIdDtoModel;

  factory UpdateByUserIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$UpdateByUserIdDtoModelFromJson(json);
}
