import { ApiProperty } from '@nestjs/swagger';
import { NotificationEnum } from 'src/constants/enums/notification-enum';
import { AccountTypeEnum } from 'src/entities';

export class GetNotificationsReturn {
  @ApiProperty({
    type: 'array',
    items: {
      type: 'object',
      properties: {
        notificationType: {
          type: 'string',
          enum: Object.values(NotificationEnum),
        },
        notificationId: { type: 'string' },
        createdAt: { type: 'string' },

        accountType: { type: 'string', enum: Object.values(AccountTypeEnum), description: 'page ya da user' },
        accountId: { type: 'string', description: "İrem Başoğlu'nun ID'si" },
        accountUsername: { type: 'string', description: 'İrem Başoğlu' },
        accountAvatarUrl: {
          type: 'string',
          description: "İrem Başoğlu'nun avatar URL'si",
        },

        contentType: { type: 'string', description: 'vibe' },
        contentId: { type: 'string', description: "Vibe'ın ID'si" },
        contentThumbnailUrl: {
          type: 'string',
          description: "Vibe'ın thumbnail URL'si",
        },
        contentName: { type: 'string', description: "Teoman Konseri Vibe'ı" },
        contentItem: { type: 'string', description: '3 Adet Memory' },

        actionType: { type: 'string', description: 'vibe' },
        actionId: {
          type: 'string',
          description: "Vibe'ın ID'si, aynı zamanda list item'a dokunulduğunda gidilecek itemin ID'si",
        },
      },
      description: "Örnek: İrem Başoğlu, Teoman Konseri Vibe'ınıza medya ekledi.",
    },
  })
  notifications!: {
    notificationType: NotificationEnum;
    notificationId: string;
    createdAt: string;

    accountType: AccountTypeEnum; // page ya da user
    accountId: string; // İrem Başoğlu'nun ID'si
    accountUsername: string; // İrem Başoğlu
    accountAvatarUrl: string; // İrem Başoğlu'nun avatar URL'si

    contentType: string; // vibe
    contentId: string; // Vibe'ın ID'si, aynı zamanda list item'a dokunulduğunda gidilecek itemin ID'si
    contentThumbnailUrl: string; // Vibe'ın thumbnail URL'si
    contentName: string; // Teoman Konseri Vibe'ı
    contentItem: string; // 3 Adet Memory

    actionType: string; // vibe
    actionId: string; // Vibe'ın ID'si
  }[];

  /*
    İrem Başoğlu, Teoman Konseri Vibe'ınıza medya ekledi.
  */

  @ApiProperty({ type: 'number' })
  notificationCount!: number;
}
