import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID } from 'class-validator';

export class InviteFriendsByIventIdDto {
  @ApiProperty({ type: 'array', items: { type: 'string' } })
  @IsArray()
  @IsUUID('4', { each: true })
  groupIds!: string[];

  @ApiProperty({ type: 'array', items: { type: 'string' } })
  @IsArray()
  @IsUUID('4', { each: true })
  userIds!: string[];
}

export class JoinIventAndCreateSquadByIventIdDto {
  @ApiProperty({ type: 'array', items: { type: 'string' } })
  @IsArray()
  @IsUUID('4', { each: true })
  groupIds!: string[];

  @ApiProperty({ type: 'array', items: { type: 'string' } })
  @IsArray()
  @IsUUID('4', { each: true })
  userIds!: string[];
}
