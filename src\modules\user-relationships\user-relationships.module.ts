import { Modu<PERSON> } from '@nestjs/common';
import { NotificationsModule } from '../notifications/notifications.module';
import { UserRelationshipsController } from './user-relationships.controller';
import { UserRelationshipsService } from './user-relationships.service';

@Module({
  imports: [NotificationsModule],
  providers: [UserRelationshipsService],
  controllers: [UserRelationshipsController],
  exports: [],
})
export class UserRelationshipsModule {}
