// Generated Dart model for RegisterDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'registerdto_model.freezed.dart';
part 'registerdto_model.g.dart';

@freezed
class RegisterDtoModel with _$RegisterDtoModel {
  const factory RegisterDtoModel({
    required String phoneNumber,
    required String fullname,
    required List<String> hobbyIds,
    required List<String> phoneNumbers,
    required String followerId,
    required List<String> phoneNumbers,
    required String newUsername,
    required String newBirthday,
    required String newGender,
    required String newAvatarUrl,
    required String newEmail,
    required String newGrad,
    required String newPhoneNumber,
  }) = _RegisterDtoModel;

  factory RegisterDtoModel.fromJson(Map<String, dynamic> json) =>
      _$RegisterDtoModelFromJson(json);
}
