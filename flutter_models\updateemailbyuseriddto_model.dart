// Generated Dart model for UpdateEmailByUserIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'updateemailbyuseriddto_model.freezed.dart';
part 'updateemailbyuseriddto_model.g.dart';

@freezed
class UpdateEmailByUserIdDtoModel with _$UpdateEmailByUserIdDtoModel {
  const factory UpdateEmailByUserIdDtoModel({
    required String newEmail,
  }) = _UpdateEmailByUserIdDtoModel;

  factory UpdateEmailByUserIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$UpdateEmailByUserIdDtoModelFromJson(json);
}
