// Generated Dart model for ValidateReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'validatereturn_model.freezed.dart';
part 'validatereturn_model.g.dart';

@freezed
class ValidateReturnModel with _$ValidateReturnModel {
  const factory ValidateReturnModel({
    String? token,
    String? userId,
    String? role,
    String? username,
    String? fullname,
    String? avatarUrl,
    required String type,
  }) = _ValidateReturnModel;

  factory ValidateReturnModel.fromJson(Map<String, dynamic> json) =>
      _$ValidateReturnModelFromJson(json);
}
