// Generated Dart model for CreateLocationReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'createlocationreturn_model.freezed.dart';
part 'createlocationreturn_model.g.dart';

@freezed
class CreateLocationReturnModel with _$CreateLocationReturnModel {
  const factory CreateLocationReturnModel({
    required String locationId,
  }) = _CreateLocationReturnModel;

  factory CreateLocationReturnModel.fromJson(Map<String, dynamic> json) =>
      _$CreateLocationReturnModelFromJson(json);
}
