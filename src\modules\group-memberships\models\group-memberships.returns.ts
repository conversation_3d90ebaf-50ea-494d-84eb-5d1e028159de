import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { UserListItem } from 'src/models/user-list-item';

export class SearchGroupMembersByGroupIdReturn {
  @ApiProperty({
    allOf: [
      { $ref: getSchemaPath(UserListItem) },
      {
        type: 'object',
        properties: { role: { type: 'string' }, isFriend: { type: 'boolean' } },
      },
    ],
  })
  members!: (UserListItem & { role: string; isFriend: boolean })[];

  @ApiProperty({ type: 'number' })
  memberCount!: number;
}

export class SearchInvitableUsersByGroupIdReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(UserListItem) },
  })
  users!: UserListItem[];

  @ApiProperty({ type: 'number' })
  userCount!: number;
}

export class SearchUsersForGroupCreationReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(UserListItem) },
  })
  users!: UserListItem[];

  @ApiProperty({ type: 'number' })
  userCount!: number;
}
