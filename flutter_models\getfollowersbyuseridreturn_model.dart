// Generated Dart model for GetFollowersByUserIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getfollowersbyuseridreturn_model.freezed.dart';
part 'getfollowersbyuseridreturn_model.g.dart';

@freezed
class GetFollowersByUserIdReturnModel with _$GetFollowersByUserIdReturnModel {
  const factory GetFollowersByUserIdReturnModel({
    required List<String> friendFirstnames,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int friendCount,
    @<PERSON><PERSON><PERSON>ey(fromJson: _stringToInt, toJson: _intToString)
    required int followerCount,
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFirstPerson,
    required List<String> followings,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int followingCount,
    required List<String> ivents,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFirstPerson,
    required String levelInfo,
    required List<Map<String, dynamic>> pages,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int pageCount,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int memoryFolderCount,
    required String userId,
    required String userRole,
    required String username,
    required String fullname,
    String? avatarUrl,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int iventCount,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int friendCount,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int followerCount,
    required List<String> hobbies,
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFollowing,
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFriend,
    @JsonKey(fromJson: _stringToBool, toJson: _boolToString)
    required bool isFirstPerson,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int vibeFolderCount,
    required String userId,
    required String token,
    required String role,
    required String username,
    required String fullname,
    String? avatarUrl,
    required String userId,
    required String username,
    String? avatarUrl,
    required String fullname,
  }) = _GetFollowersByUserIdReturnModel;

  factory GetFollowersByUserIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetFollowersByUserIdReturnModelFromJson(json);
}
