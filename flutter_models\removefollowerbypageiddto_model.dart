// Generated Dart model for RemoveFollowerByPageIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'removefollowerbypageiddto_model.freezed.dart';
part 'removefollowerbypageiddto_model.g.dart';

@freezed
class RemoveFollowerByPageIdDtoModel with _$RemoveFollowerByPageIdDtoModel {
  const factory RemoveFollowerByPageIdDtoModel({
    required String userId,
  }) = _RemoveFollowerByPageIdDtoModel;

  factory RemoveFollowerByPageIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$RemoveFollowerByPageIdDtoModelFromJson(json);
}
