import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { EmptyReturn } from 'src/models/empty-return';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { DataSource } from 'typeorm';
import { CreateGroupParams, DeleteGroupByGroupIdParams, GetGroupByGroupIdParams } from './models/groups.params';
import { CreateGroupReturn, GetGroupByGroupIdReturn } from './models/groups.returns';

@Injectable()
export class GroupsService {
  constructor(private dataSource: DataSource) {}

  async createGroup(
    // TODO
    createGroupParams: CreateGroupParams,
  ): Promise<CreateGroupReturn> {
    const { sessionId, sessionRole, groupName, thumbnailBuffer } = createGroupParams;

    const groupInsertResult = await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'groups',
        values: {
          creator_id: sessionId,
          group_name: groupName,
          // thumbnail_url: thumbnailBuffer,  // TODO: upload to s3
        },
      }),
    );
    const insertedGroupId = groupInsertResult[0].id;

    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'group_memberships',
        values: {
          member_id: sessionId,
          group_id: insertedGroupId,
          status: 'admin',
        },
      }),
    );

    return { groupId: insertedGroupId };
  }

  async deleteGroupByGroupId(deleteGroupByGroupIdParams: DeleteGroupByGroupIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, groupId } = deleteGroupByGroupIdParams;

    const membershipResult = await this.dataSource.query(`
      SELECT
          id AS group_membership_id
          group_id
      FROM group_memberships
      WHERE group_id = '${groupId}'
      AND status IN ('admin', 'moderator')
      AND member_id = '${sessionId}';
    `);
    if (membershipResult.length === 0) {
      throw new HttpException('You are not authorized', HttpStatus.BAD_REQUEST);
    }

    await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('groups')
      .where({
        id: membershipResult[0].group_id,
      })
      .execute();

    return {};
  }

  async getGroupByGroupId(getGroupByGroupIdParams: GetGroupByGroupIdParams): Promise<GetGroupByGroupIdReturn> {
    const { sessionId, sessionRole, groupId } = getGroupByGroupIdParams;

    const groupResult = await this.dataSource.query(`
      SELECT
          g.id AS group_id,
          g.group_name AS group_name,
          g.thumbnail_url AS thumbnail_url,
          COUNT(gm.member_id) AS membership_count
      FROM groups g
      LEFT JOIN group_memberships gm ON gm.group_id = g.id
      WHERE g.id = '${groupId}'
      GROUP BY g.id;
    `);

    const membersResult = await this.dataSource.query(`
      WITH FriendshipsOfSessionUser AS (
          -- Record of whether the session user is friend with the listed user, grouped by user
          SELECT
              COUNT(friend_id) AS is_friend,
              friend_id
          FROM user_friendships
          WHERE user_id = '${sessionId}'
          GROUP BY friend_id
      ),
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university,
          gm.status AS role,
          fosu.is_friend AS is_friend
      FROM group_members gm
      LEFT JOIN groups g ON g.id = gm.group_id
      LEFT JOIN users u ON u.id = gm.member_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code_code
      LEFT JOIN FriendshipsOfSessionUser fosu ON fosu.friend_id = gm.member_id
      WHERE g.id = '${groupId}'
      AND gm.status IN ('accepted', 'moderator', 'admin');
    `);

    return {
      groupId: groupResult[0].group_id,
      groupName: groupResult[0].group_name,
      thumbnailUrl: groupResult[0].thumbnail_url,
      members: membersResult.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_Url,
        university: val.university,
        role: val.role,
        isFriend: val.is_friend ? true : false,
      })),
      memberCount: groupResult[0].membership_count,
    };
  }
}
