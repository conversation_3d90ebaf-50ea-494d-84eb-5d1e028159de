// Generated Dart model for SearchPageBlocklistByPageIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchpageblocklistbypageidreturn_model.freezed.dart';
part 'searchpageblocklistbypageidreturn_model.g.dart';

@freezed
class SearchPageBlocklistByPageIdReturnModel with _$SearchPageBlocklistByPageIdReturnModel {
  const factory SearchPageBlocklistByPageIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int userCount,
  }) = _SearchPageBlocklistByPageIdReturnModel;

  factory SearchPageBlocklistByPageIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchPageBlocklistByPageIdReturnModelFromJson(json);
}
