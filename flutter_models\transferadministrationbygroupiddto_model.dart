// Generated Dart model for TransferAdministrationByGroupIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'transferadministrationbygroupiddto_model.freezed.dart';
part 'transferadministrationbygroupiddto_model.g.dart';

@freezed
class TransferAdministrationByGroupIdDtoModel with _$TransferAdministrationByGroupIdDtoModel {
  const factory TransferAdministrationByGroupIdDtoModel({
    required String userId,
  }) = _TransferAdministrationByGroupIdDtoModel;

  factory TransferAdministrationByGroupIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$TransferAdministrationByGroupIdDtoModelFromJson(json);
}
