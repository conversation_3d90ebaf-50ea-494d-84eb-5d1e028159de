// Generated Dart model for GetNotificationsReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getnotificationsreturn_model.freezed.dart';
part 'getnotificationsreturn_model.g.dart';

@freezed
class GetNotificationsReturnModel with _$GetNotificationsReturnModel {
  const factory GetNotificationsReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int notificationCount,
  }) = _GetNotificationsReturnModel;

  factory GetNotificationsReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetNotificationsReturnModelFromJson(json);
}
