import os
import re
import shutil
from typing import Any, Callable, Union


def get_folders(path):
    return [i for i in os.listdir(path) if os.path.isdir(os.path.join(path, i))]


def get_files(path):
    return [i for i in os.listdir(path) if os.path.isfile(os.path.join(path, i))]


def read_file(filename: str) -> str:
    with open(filename, "r", encoding="utf-8") as txt_file:
        return txt_file.read()


def write_file(content: str, filename: str, *args, **kwargs) -> None:
    try:
        dir = os.path.dirname(os.path.realpath(filename))
        os.makedirs(dir, exist_ok=True)
        with open(filename, "w", encoding="utf-8") as txt_file:
            txt_file.write(content)
        print(f"Data is written to {filename}")
    except Exception as e:
        print(f"Error writing data to {filename}:\n{e}")


def dprint(
    data: dict,
    sep: Union[str, None] = "\n",
    end: Union[str, None] = "\n",
    start: Union[str, None] = "",
    keys: bool = False,
) -> None:
    print(start, end="")
    items = data.items()
    if keys:
        for idx, (k, v) in enumerate(items):
            print(f"{k}: {v}", end=sep if idx + 1 < len(items) else "")
    else:
        for idx, (k, v) in enumerate(items):
            print(f"{v}", end=sep if idx + 1 < len(items) else "")
    print(end, end="")


def lprint(
    data: list,
    sep: Union[str, None] = "\n",
    end: Union[str, None] = "\n",
    start: Union[str, None] = "",
    index: bool = False,
    fun: Callable[[Any], Any] = None,
) -> None:
    print(start, end="")
    for idx, item in enumerate(data):
        start = f"[{idx}] " if index else ""
        item = item if fun is None else fun(item)
        print(f"{start}{item}", end=sep if idx + 1 < len(data) else "")
    print(end, end="")


modules_path = "src/modules"
modules = get_folders(modules_path)

for module in modules:
    models_path = os.path.join(modules_path, module, "models")
    if not os.path.exists(models_path):
        continue
    model_folders = get_folders(models_path)
    index_file_path = os.path.join(modules_path, module, "models", "index.ts")

    index_content = (
        "\n".join(
            [
                f"export * from './{module}.{model_folder}';"
                for model_folder in model_folders
            ]
        )
        + "\n"
    )
    write_file(index_content, index_file_path)
    
    for model_folder in model_folders:
        model_folder_path = os.path.join(models_path, model_folder)
        if os.path.isdir(model_folder_path):
            shutil.rmtree(model_folder_path)

    # for model_folder in model_folders:
    #     model_file_name = f"{module}.{model_folder}.ts"

    #     model_file_content = "\n".join([read_file(os.path.join(models_path, model_folder, file)) for file in get_files(os.path.join(models_path, model_folder))])
    #     write_file(model_file_content, os.path.join(models_path, model_file_name))
