// Generated Dart model for CreateMemoryReturn
import 'package:freezed_annotation/freezed_annotation.dart';

part 'creatememoryreturn_model.freezed.dart';
part 'creatememoryreturn_model.g.dart';

@freezed
class CreateMemoryReturnModel with _$CreateMemoryReturnModel {
  const factory CreateMemoryReturnModel({
    required String memoryId,
  }) = _CreateMemoryReturnModel;

  factory CreateMemoryReturnModel.fromJson(Map<String, dynamic> json) =>
      _$CreateMemoryReturnModelFromJson(json);
}
