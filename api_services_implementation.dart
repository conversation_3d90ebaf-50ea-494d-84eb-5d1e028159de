// Complete API Services Implementation for Ivent API
// Handles all major endpoints with proper error handling and type safety

import 'package:dio/dio.dart';
import 'dart_models_examples.dart';

// ============================================================================
// BASE API RESPONSE WRAPPER
// ============================================================================

@freezed
class ApiResponse<T> with _$ApiResponse<T> {
  const factory ApiResponse({
    required int status,
    required String message,
    required T data,
  }) = _ApiResponse<T>;

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object?) fromJsonT,
  ) => _$ApiResponseFromJson(json, fromJsonT);
}

// ============================================================================
// API CLIENT CONFIGURATION
// ============================================================================

class ApiClient {
  static const String baseUrl = 'https://your-api-domain.com';
  late final Dio _dio;

  ApiClient() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add auth token if available
        final token = await _getAuthToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        if (error.response?.statusCode == 401) {
          await _handleTokenExpiry();
        }
        handler.next(error);
      },
    ));
  }

  Future<String?> _getAuthToken() async {
    // Implement token retrieval from secure storage
    return null;
  }

  Future<void> _handleTokenExpiry() async {
    // Handle token expiry (logout, refresh, etc.)
  }

  Dio get dio => _dio;
}

// ============================================================================
// USER SERVICE
// ============================================================================

class UserService {
  final ApiClient _apiClient;

  UserService(this._apiClient);

  Future<ApiResponse<RegisterReturn>> register(RegisterDto dto) async {
    try {
      final response = await _apiClient.dio.post(
        '/users/register',
        data: dto.toJson(),
      );
      
      return ApiResponse.fromJson(
        response.data,
        (json) => RegisterReturn.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<ApiResponse<UserModel>> getUserById(String userId) async {
    try {
      final response = await _apiClient.dio.get('/users/$userId');
      
      return ApiResponse.fromJson(
        response.data,
        (json) => UserModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<ApiResponse<List<UserModel>>> searchUsers({
    required String query,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.dio.get(
        '/users/search',
        queryParameters: {
          'q': query,
          'page': page,
          'limit': limit,
        },
      );
      
      return ApiResponse.fromJson(
        response.data,
        (json) => (json as List)
            .map((item) => UserModel.fromJson(item))
            .toList(),
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<ApiResponse<void>> followUser(String userId) async {
    try {
      final response = await _apiClient.dio.post('/users/$userId/follow');
      
      return ApiResponse.fromJson(
        response.data,
        (json) => null,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<ApiResponse<void>> unfollowUser(String userId) async {
    try {
      final response = await _apiClient.dio.post('/users/$userId/unfollow');
      
      return ApiResponse.fromJson(
        response.data,
        (json) => null,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<ApiResponse<List<UserModel>>> getFollowers({
    required String userId,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.dio.get(
        '/users/$userId/followers',
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );
      
      return ApiResponse.fromJson(
        response.data,
        (json) => (json['followers'] as List)
            .map((item) => UserModel.fromJson(item))
            .toList(),
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<ApiResponse<void>> updateProfile({
    required String userId,
    String? fullname,
    String? bio,
    String? avatarUrl,
    UserGender? gender,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (fullname != null) data['fullname'] = fullname;
      if (bio != null) data['bio'] = bio;
      if (avatarUrl != null) data['avatarUrl'] = avatarUrl;
      if (gender != null) data['gender'] = gender.name.toUpperCase();

      final response = await _apiClient.dio.put(
        '/users/$userId/update',
        data: data,
      );
      
      return ApiResponse.fromJson(
        response.data,
        (json) => null,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
}

// ============================================================================
// IVENT SERVICE
// ============================================================================

class IventService {
  final ApiClient _apiClient;

  IventService(this._apiClient);

  Future<ApiResponse<String>> createIvent(CreateIventDto dto) async {
    try {
      final response = await _apiClient.dio.post(
        '/ivents/create',
        data: dto.toJson(),
      );
      
      return ApiResponse.fromJson(
        response.data,
        (json) => json['iventId'] as String,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<ApiResponse<List<IventModel>>> getLatestIvents({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.dio.get(
        '/ivents/latest',
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );
      
      return ApiResponse.fromJson(
        response.data,
        (json) => (json['ivents'] as List)
            .map((item) => IventModel.fromJson(item))
            .toList(),
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<ApiResponse<IventModel>> getIventById(String iventId) async {
    try {
      final response = await _apiClient.dio.get('/ivents/$iventId');
      
      return ApiResponse.fromJson(
        response.data,
        (json) => IventModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<ApiResponse<void>> joinIvent(String iventId) async {
    try {
      final response = await _apiClient.dio.post('/ivents/$iventId/join');
      
      return ApiResponse.fromJson(
        response.data,
        (json) => null,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<ApiResponse<void>> leaveIvent(String iventId) async {
    try {
      final response = await _apiClient.dio.post('/ivents/$iventId/leave');
      
      return ApiResponse.fromJson(
        response.data,
        (json) => null,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<ApiResponse<void>> favoriteIvent(String iventId) async {
    try {
      final response = await _apiClient.dio.post('/ivents/$iventId/favorite');
      
      return ApiResponse.fromJson(
        response.data,
        (json) => null,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<ApiResponse<List<IventModel>>> searchIvents({
    required String query,
    String? category,
    double? latitude,
    double? longitude,
    double? radius,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'q': query,
        'page': page,
        'limit': limit,
      };
      
      if (category != null) queryParams['category'] = category;
      if (latitude != null) queryParams['lat'] = latitude;
      if (longitude != null) queryParams['lng'] = longitude;
      if (radius != null) queryParams['radius'] = radius;

      final response = await _apiClient.dio.get(
        '/ivents/search',
        queryParameters: queryParams,
      );
      
      return ApiResponse.fromJson(
        response.data,
        (json) => (json['ivents'] as List)
            .map((item) => IventModel.fromJson(item))
            .toList(),
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }
}

// ============================================================================
// ERROR HANDLING
// ============================================================================

ApiException _handleError(DioException e) {
  switch (e.type) {
    case DioExceptionType.connectionTimeout:
    case DioExceptionType.receiveTimeout:
      return ApiException('Connection timeout');
    case DioExceptionType.badResponse:
      final statusCode = e.response?.statusCode;
      final message = e.response?.data?['message'] ?? 'Unknown error';
      return ApiException('Server error ($statusCode): $message');
    default:
      return ApiException('Network error: ${e.message}');
  }
}

class ApiException implements Exception {
  final String message;
  ApiException(this.message);
  
  @override
  String toString() => 'ApiException: $message';
}
