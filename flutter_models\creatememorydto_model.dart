// Generated Dart model for CreateMemoryDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'creatememorydto_model.freezed.dart';
part 'creatememorydto_model.g.dart';

@freezed
class CreateMemoryDtoModel with _$CreateMemoryDtoModel {
  const factory CreateMemoryDtoModel({
    String? caption,
    required String squadId,
  }) = _CreateMemoryDtoModel;

  factory CreateMemoryDtoModel.fromJson(Map<String, dynamic> json) =>
      _$CreateMemoryDtoModelFromJson(json);
}
