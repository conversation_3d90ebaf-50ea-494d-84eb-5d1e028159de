import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { UserListItem } from 'src/models/user-list-item';

export class SearchModeratorsForPageCreationReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
  })
  users!: UserListItem[];

  @ApiProperty({ type: 'number' })
  userCount!: number;
}

export class SearchModeratorsToAddByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
  })
  users!: UserListItem[];

  @ApiProperty({ type: 'number' })
  userCount!: number;
}

export class SearchPageAdminsByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      allOf: [
        { $ref: getSchemaPath(UserListItem) },
        {
          type: 'object',
          properties: { role: { type: 'string' } },
        },
      ],
    },
  })
  users!: (UserListItem & { role: string })[];

  @ApiProperty({ type: 'number' })
  userCount!: number;
}

export class SearchPageMembersByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
  })
  users!: UserListItem[];

  @ApiProperty({ type: 'number' })
  userCount!: number;
}

export class SearchUsersToAddByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
  })
  users!: UserListItem[];

  @ApiProperty({ type: 'number' })
  userCount!: number;
}

export class SearchAdministrationByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      allOf: [
        { $ref: getSchemaPath(UserListItem) },
        {
          type: 'object',
          properties: { role: { type: 'string' } },
        },
      ],
    },
  })
  users!: (UserListItem & { role: string })[];

  @ApiProperty({ type: 'number' })
  userCount!: number;
}
