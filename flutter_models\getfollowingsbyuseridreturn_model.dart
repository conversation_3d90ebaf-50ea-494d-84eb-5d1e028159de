// Generated Dart model for GetFollowingsByUserIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getfollowingsbyuseridreturn_model.freezed.dart';
part 'getfollowingsbyuseridreturn_model.g.dart';

@freezed
class GetFollowingsByUserIdReturnModel with _$GetFollowingsByUserIdReturnModel {
  const factory GetFollowingsByUserIdReturnModel({
    required List<String> followings,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int followingCount,
  }) = _GetFollowingsByUserIdReturnModel;

  factory GetFollowingsByUserIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetFollowingsByUserIdReturnModelFromJson(json);
}
