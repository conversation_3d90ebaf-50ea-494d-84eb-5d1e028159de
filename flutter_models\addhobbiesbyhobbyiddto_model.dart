// Generated Dart model for AddHobbiesByHobbyIdDto
import 'package:freezed_annotation/freezed_annotation.dart';

part 'addhobbiesbyhobbyiddto_model.freezed.dart';
part 'addhobbiesbyhobbyiddto_model.g.dart';

@freezed
class AddHobbiesByHobbyIdDtoModel with _$AddHobbiesByHobbyIdDtoModel {
  const factory AddHobbiesByHobbyIdDtoModel({
    required List<String> hobbyIds,
  }) = _AddHobbiesByHobbyIdDtoModel;

  factory AddHobbiesByHobbyIdDtoModel.fromJson(Map<String, dynamic> json) =>
      _$AddHobbiesByHobbyIdDtoModelFromJson(json);
}
