// Generated Dart model for SearchModeratorsForPageCreationReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchmoderatorsforpagecreationreturn_model.freezed.dart';
part 'searchmoderatorsforpagecreationreturn_model.g.dart';

@freezed
class SearchModeratorsForPageCreationReturnModel with _$SearchModeratorsForPageCreationReturnModel {
  const factory SearchModeratorsForPageCreationReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int userCount,
  }) = _SearchModeratorsForPageCreationReturnModel;

  factory SearchModeratorsForPageCreationReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchModeratorsForPageCreationReturnModelFromJson(json);
}
