// Generated Dart model for GetLatestLocationsReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'getlatestlocationsreturn_model.freezed.dart';
part 'getlatestlocationsreturn_model.g.dart';

@freezed
class GetLatestLocationsReturnModel with _$GetLatestLocationsReturnModel {
  const factory GetLatestLocationsReturnModel({
    required List<Map<String, dynamic>> locations,
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int locationCount,
  }) = _GetLatestLocationsReturnModel;

  factory GetLatestLocationsReturnModel.fromJson(Map<String, dynamic> json) =>
      _$GetLatestLocationsReturnModelFromJson(json);
}
