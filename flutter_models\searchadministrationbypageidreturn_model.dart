// Generated Dart model for SearchAdministrationByPageIdReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchadministrationbypageidreturn_model.freezed.dart';
part 'searchadministrationbypageidreturn_model.g.dart';

@freezed
class SearchAdministrationByPageIdReturnModel with _$SearchAdministrationByPageIdReturnModel {
  const factory SearchAdministrationByPageIdReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int userCount,
  }) = _SearchAdministrationByPageIdReturnModel;

  factory SearchAdministrationByPageIdReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchAdministrationByPageIdReturnModelFromJson(json);
}
