// Generated Dart model for SearchCollabsForIventCreationReturn
import 'package:freezed_annotation/freezed_annotation.dart';

import 'model_helpers.dart';

part 'searchcollabsforiventcreationreturn_model.freezed.dart';
part 'searchcollabsforiventcreationreturn_model.g.dart';

@freezed
class SearchCollabsForIventCreationReturnModel with _$SearchCollabsForIventCreationReturnModel {
  const factory SearchCollabsForIventCreationReturnModel({
    @JsonKey(fromJson: _stringToInt, toJson: _intToString)
    required int accountCount,
  }) = _SearchCollabsForIventCreationReturnModel;

  factory SearchCollabsForIventCreationReturnModel.fromJson(Map<String, dynamic> json) =>
      _$SearchCollabsForIventCreationReturnModelFromJson(json);
}
