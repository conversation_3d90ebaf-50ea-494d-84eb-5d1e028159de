import { ApiProperty } from '@nestjs/swagger';

export class MemoryItem {
  @ApiProperty({ type: 'string' })
  memoryId!: string;

  @ApiProperty({ type: 'string' })
  mediaUrl!: string;

  @ApiProperty({ type: 'string', nullable: true })
  thumbnailUrl!: string | null;

  @ApiProperty({ type: 'string' })
  caption!: string;

  @ApiProperty({ type: 'string' })
  creatorId!: string;

  @ApiProperty({ type: 'string' })
  creatorUsername!: string;

  @ApiProperty({ type: 'string' })
  creatorAvatarUrl!: string;

  @ApiProperty({ type: 'string' })
  iventId!: string;

  @ApiProperty({ type: 'string' })
  iventName!: string;

  @ApiProperty({ type: 'date', nullable: true })
  date!: Date | null;

  @ApiProperty({ type: 'number', nullable: true })
  memberCount!: number | null;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    nullable: true,
  })
  memberNames!: string[] | null;
}
